# ZNS Token Refresh Implementation

## Tổng quan
Triển khai hệ thống tự động làm mới ZNS access token từ Zalo OAuth API với các thành phần:

1. **User Service**: Endpoint để refresh token thủ công
2. **Job Schedule Service**: Job tự động refresh token theo lịch trình
3. **Configuration Management**: Lưu trữ và cập nhật token trong database

## Các thành phần đã thêm

### 1. User Service (@user)

#### Endpoints mới:
- `POST /api/v1/otp/refresh-token` - Làm mới ZNS access token

#### Classes mới:
- `ZnsTokenService` & `ZnsTokenServiceImpl` - Service xử lý refresh token
- `RefreshTokenRequestDto` & `RefreshTokenResponseDto` - DTOs cho request/response
- `ZaloOAuthFeignClient` - Feign client gọi Zalo OAuth API

#### Cập nhật:
- `ConfigurationService` - Thêm method `updateConfigurationValue()`
- `OtpController` - Thêm endpoint refresh token

### 2. Job Schedule Service (@job-schedule)

#### Classes mới:
- `ZnsTokenRefreshJob` - Job refresh token định kỳ
- `UserServiceClient` - Feign client gọi User Service

#### Endpoints mới:
- `POST /tasks/zns-token-refresh` - Tạo job refresh token (mỗi 10 giây)

#### Cập nhật:
- `JobService` - Thêm method `scheduleZnsTokenRefreshJob()`
- `TaskService` - Thêm method `createZnsTokenRefreshTask()`

## Cấu hình

### Environment Variables
```properties
# ZNS Configuration
zns.app.id=3676483894335813334
zns.secret.key=SG5T7VSWI9q8L34CGE2t
zns.otp.template.id=435884

# Feign Configuration (job-schedule)
feign.user.url=https://api.pronexus.vn/user
```

### Database Configuration Keys
- `ZNS_ACCESS_TOKEN` - Access token hiện tại
- `ZNS_FRESH_TOKEN` - Refresh token để làm mới

## Cách sử dụng

### 1. Tạo Job tự động (Production)
```bash
curl -X POST https://api.pronexus.vn/job/tasks/zns-token-refresh
```

### 2. Refresh token thủ công
```bash
curl -X POST https://api.pronexus.vn/user/api/v1/otp/refresh-token
```

### 3. Thay đổi tần suất job
Sửa cron expression trong `TaskController.createZnsTokenRefreshJob()`:
- Hiện tại: `*/10 * * * * ?` (mỗi 10 giây)
- Production: `0 */30 * * * ?` (mỗi 30 phút)

## API Response Format

### Refresh Token Success
```json
{
  "success": true,
  "message": "Refresh token thành công",
  "data": {
    "accessToken": "new_access_token_here",
    "refreshToken": "new_refresh_token_here", 
    "expiresIn": "90000"
  }
}
```

### Job Creation Success
```json
{
  "id": "uuid",
  "name": "ZNS_TOKEN_REFRESH",
  "group": "ZNS_GROUP",
  "cronExpression": "*/10 * * * * ?"
}
```

## Testing

Import Postman collection: `user/ZNS_Token_Refresh.postman_collection.json`

### Test Variables:
- `base_url`: https://api.pronexus.vn/user
- `job_base_url`: https://api.pronexus.vn/job

## Monitoring

Kiểm tra logs để theo dõi:
- User Service: `ZnsTokenServiceImpl` logs
- Job Schedule: `ZnsTokenRefreshJob` logs
- Database: Cập nhật `ZNS_ACCESS_TOKEN` trong bảng configurations

## Troubleshooting

### Lỗi thường gặp:
1. **Không tìm thấy ZNS_FRESH_TOKEN**: Cần thêm refresh token vào database
2. **Feign connection error**: Kiểm tra network và URL configuration
3. **Job không chạy**: Kiểm tra Quartz scheduler và database connection

### Debug commands:
```bash
# Kiểm tra job đang chạy
curl https://api.pronexus.vn/job/tasks

# Kiểm tra configuration
SELECT * FROM portal.configurations WHERE config_key IN ('ZNS_ACCESS_TOKEN', 'ZNS_FRESH_TOKEN');
```
