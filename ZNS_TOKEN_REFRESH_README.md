# ZNS Token Refresh Implementation

## Tổng quan
Triển khai hệ thống tự động làm mới ZNS access token từ Zalo OAuth API với thiết kế tách biệt:

1. **User Service**: Endpoint để refresh token thủ công
2. **Job Schedule Service**: <PERSON><PERSON><PERSON><PERSON> lý tasks và jobs với thiết kế linh hoạt
3. **Configuration Management**: Lưu trữ và cập nhật token trong database

## Thiết kế mới (Tách biệt Task Creation và Job Execution)

### Workflow:
1. **Tạo Task**: Insert record vào bảng `tasks` (chỉ lưu thông tin, chưa chạy)
2. **Đăng ký Job**: API riêng để schedule job từ task đã có trong DB
3. **Quản lý**: C<PERSON> thể start/stop job mà không mất thông tin task

## Cá<PERSON> thành phần đã thêm

### 1. User Service (@user)

#### Endpoints mới:
- `POST /api/v1/otp/refresh-token` - <PERSON><PERSON>m mới ZNS access token

#### Classes mới:
- `ZnsTokenService` & `ZnsTokenServiceImpl` - Service xử lý refresh token
- `RefreshTokenRequestDto` & `RefreshTokenResponseDto` - DTOs cho request/response
- `ZaloOAuthFeignClient` - Feign client gọi Zalo OAuth API

#### Cập nhật:
- `ConfigurationService` - Thêm method `updateConfigurationValue()`
- `OtpController` - Thêm endpoint refresh token

### 2. Job Schedule Service (@job-schedule)

#### Classes mới:
- `ZnsTokenRefreshJob` - Job refresh token định kỳ
- `UserServiceClient` - Feign client gọi User Service
- `JobType` enum - Phân loại các loại job

#### Endpoints mới:
- `GET /tasks` - Lấy tất cả tasks
- `POST /tasks` - Tạo task (chỉ lưu DB)
- `POST /tasks/{id}/schedule` - Schedule task đã có
- `POST /tasks/{id}/unschedule` - Unschedule task
- `POST /tasks/zns-token-refresh` - Tạo ZNS task (chỉ lưu DB)
- `POST /tasks/zns-token-refresh/schedule` - Tạo và schedule ZNS task

#### Cập nhật:
- `Task` entity - Thêm `jobType` và `isActive` fields
- `TaskService` - Tách biệt create và schedule logic
- `JobService` - Hỗ trợ nhiều loại job

## Cấu hình

### Environment Variables
```properties
# ZNS Configuration
zns.app.id=3676483894335813334
zns.secret.key=SG5T7VSWI9q8L34CGE2t
zns.otp.template.id=435884

# Feign Configuration (job-schedule)
feign.user.url=https://api.pronexus.vn/user
```

### Database Configuration Keys
- `ZNS_ACCESS_TOKEN` - Access token hiện tại
- `ZNS_FRESH_TOKEN` - Refresh token để làm mới

## Cách sử dụng

### 1. Workflow mới (Khuyến nghị)

#### Bước 1: Tạo task (chỉ lưu DB)
```bash
curl -X POST https://api.pronexus.vn/job/tasks/zns-token-refresh
```

#### Bước 2: Lấy task ID từ response và schedule
```bash
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/schedule
```

#### Bước 3: Quản lý job
```bash
# Dừng job (nhưng vẫn giữ task)
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/unschedule

# Chạy lại job
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/schedule

# Xem tất cả tasks
curl -X GET https://api.pronexus.vn/job/tasks
```

### 2. Cách nhanh (Tạo và schedule ngay)
```bash
curl -X POST https://api.pronexus.vn/job/tasks/zns-token-refresh/schedule
```

### 3. Refresh token thủ công
```bash
curl -X POST https://api.pronexus.vn/user/api/v1/otp/refresh-token
```

### 4. Tạo custom task
```bash
curl -X POST https://api.pronexus.vn/job/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "CUSTOM_TASK",
    "group": "CUSTOM_GROUP",
    "cronExpression": "0 */30 * * * ?",
    "jobType": "ZNS_TOKEN_REFRESH"
  }'
```

## API Response Format

### Refresh Token Success
```json
{
  "success": true,
  "message": "Refresh token thành công",
  "data": {
    "accessToken": "new_access_token_here",
    "refreshToken": "new_refresh_token_here", 
    "expiresIn": "90000"
  }
}
```

### Task Creation Success
```json
{
  "id": "uuid",
  "name": "ZNS_TOKEN_REFRESH",
  "group": "ZNS_GROUP",
  "cronExpression": "*/10 * * * * ?",
  "jobType": "ZNS_TOKEN_REFRESH",
  "isActive": false
}
```

### Task Scheduled Success
```json
{
  "id": "uuid",
  "name": "ZNS_TOKEN_REFRESH",
  "group": "ZNS_GROUP",
  "cronExpression": "*/10 * * * * ?",
  "jobType": "ZNS_TOKEN_REFRESH",
  "isActive": true
}
```

## Testing

Import Postman collection: `user/ZNS_Token_Refresh.postman_collection.json`

### Test Variables:
- `base_url`: https://api.pronexus.vn/user
- `job_base_url`: https://api.pronexus.vn/job

## Monitoring

Kiểm tra logs để theo dõi:
- User Service: `ZnsTokenServiceImpl` logs
- Job Schedule: `ZnsTokenRefreshJob` logs
- Database: Cập nhật `ZNS_ACCESS_TOKEN` trong bảng configurations

## Troubleshooting

### Lỗi thường gặp:
1. **Không tìm thấy ZNS_FRESH_TOKEN**: Cần thêm refresh token vào database
2. **Feign connection error**: Kiểm tra network và URL configuration
3. **Job không chạy**: Kiểm tra Quartz scheduler và database connection

### Debug commands:
```bash
# Kiểm tra tất cả tasks
curl https://api.pronexus.vn/job/tasks

# Kiểm tra task cụ thể
curl https://api.pronexus.vn/job/tasks/{task_id}

# Kiểm tra configuration
SELECT * FROM portal.configurations WHERE config_key IN ('ZNS_ACCESS_TOKEN', 'ZNS_FRESH_TOKEN');

# Kiểm tra tasks trong database
SELECT id, name, job_type, is_active, cron_expression FROM tasks;
```

## Lợi ích của thiết kế mới

1. **Tái sử dụng**: Có thể tạo nhiều task cùng loại với cron expression khác nhau
2. **Linh hoạt**: Start/stop job mà không mất thông tin task
3. **Quản lý tốt**: Xem được trạng thái tất cả tasks
4. **Mở rộng**: Dễ dàng thêm loại job mới
5. **Debug**: Có thể test từng bước một cách độc lập
