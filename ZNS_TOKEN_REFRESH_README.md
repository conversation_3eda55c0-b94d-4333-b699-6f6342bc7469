# ZNS Token Refresh Implementation

## Tổng quan
Triển khai hệ thống tự động làm mới ZNS access token từ Zalo OAuth API với thiết kế tách biệt:

1. **User Service**: Endpoint để refresh token thủ công
2. **Job Schedule Service**: <PERSON><PERSON><PERSON><PERSON> lý tasks và jobs với thiết kế linh hoạt
3. **Configuration Management**: Lưu trữ và cập nhật token trong database

## Thiết kế mới (Tách biệt Task Creation và Job Execution)

### Workflow:
1. **Tạo Task**: Insert record vào bảng `tasks` (chỉ lưu thông tin, chưa chạy)
2. **Đăng ký Job**: API riêng để schedule job từ task đã có trong DB
3. **Quản lý**: C<PERSON> thể start/stop job mà không mất thông tin task

## Cá<PERSON> thành phần đã thêm

### 1. User Service (@user)

#### Endpoints mới:
- `POST /api/v1/otp/refresh-token` - <PERSON><PERSON>m mới ZNS access token

#### Classes mới:
- `ZnsTokenService` & `ZnsTokenServiceImpl` - Service xử lý refresh token
- `RefreshTokenRequestDto` & `RefreshTokenResponseDto` - DTOs cho request/response
- `ZaloOAuthFeignClient` - Feign client gọi Zalo OAuth API

#### Cập nhật:
- `ConfigurationService` - Thêm method `updateConfigurationValue()`
- `OtpController` - Thêm endpoint refresh token

### 2. Job Schedule Service (@job-schedule)

#### Classes mới:
- `ZnsTokenRefreshJob` - Job refresh token định kỳ
- `UserServiceClient` - Feign client gọi User Service
- `JobType` enum - Phân loại các loại job

#### Endpoints mới:
- `GET /tasks` - Lấy tất cả tasks
- `POST /tasks` - Tạo task (chỉ lưu DB)
- `POST /tasks/{id}/schedule` - Schedule task đã có
- `POST /tasks/{id}/unschedule` - Unschedule task
- `POST /tasks/zns-token-refresh` - Tạo ZNS task (chỉ lưu DB)
- `POST /tasks/zns-token-refresh/schedule` - Tạo và schedule ZNS task

#### Cập nhật:
- `Task` entity - Thêm `jobType` và `isActive` fields
- `TaskService` - Tách biệt create và schedule logic
- `JobService` - Hỗ trợ nhiều loại job

## Cấu hình

### Environment Variables
```properties
# ZNS Configuration
zns.app.id=3676483894335813334
zns.secret.key=SG5T7VSWI9q8L34CGE2t
zns.otp.template.id=435884

# Feign Configuration (job-schedule)
feign.user.url=https://api.pronexus.vn/user
```

### Database Configuration Keys
- `ZNS_ACCESS_TOKEN` - Access token hiện tại
- `ZNS_FRESH_TOKEN` - Refresh token để làm mới

## Cách sử dụng

### 1. Workflow mới (Khuyến nghị)

#### Bước 1: Tạo task (chỉ lưu DB)
```bash
curl -X POST https://api.pronexus.vn/job/tasks/zns-token-refresh
```

#### Bước 2: Lấy task ID từ response và schedule
```bash
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/schedule
```

#### Bước 3: Quản lý job
```bash
# Dừng job (nhưng vẫn giữ task)
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/unschedule

# Chạy lại job
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/schedule

# Xem tất cả tasks
curl -X GET https://api.pronexus.vn/job/tasks
```

### 2. Cách nhanh (Tạo và schedule ngay)
```bash
curl -X POST https://api.pronexus.vn/job/tasks/zns-token-refresh/schedule
```

### 3. Refresh token thủ công
```bash
curl -X POST https://api.pronexus.vn/user/api/v1/otp/refresh-token
```

### 4. Tạo custom task
```bash
curl -X POST https://api.pronexus.vn/job/tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "name": "CUSTOM_TASK",
    "group": "CUSTOM_GROUP",
    "cronExpression": "0 */30 * * * ?",
    "jobType": "ZNS_TOKEN_REFRESH"
  }'
```

### 5. Lấy danh sách tasks với phân trang
```bash
# Lấy tasks với phân trang mặc định (page=0, size=10, sort by createdAt DESC)
curl -X GET "https://api.pronexus.vn/job/tasks" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Lấy tasks với filter và phân trang tùy chỉnh
curl -X GET "https://api.pronexus.vn/job/tasks?name=ZNS&jobType=ZNS_TOKEN_REFRESH&isActive=true&page=0&size=5&sort=name,asc" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Lấy tất cả tasks không phân trang (backward compatibility)
curl -X GET "https://api.pronexus.vn/job/tasks/all" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 6. Quản lý tasks
```bash
# Lấy task theo ID
curl -X GET "https://api.pronexus.vn/job/tasks/{task_id}" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Schedule task
curl -X POST "https://api.pronexus.vn/job/tasks/{task_id}/schedule" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Unschedule task
curl -X POST "https://api.pronexus.vn/job/tasks/{task_id}/unschedule" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Xóa task
curl -X DELETE "https://api.pronexus.vn/job/tasks/{task_id}" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## API Response Format

### Refresh Token Success
```json
{
  "success": true,
  "message": "Refresh token thành công",
  "data": {
    "accessToken": "new_access_token_here",
    "refreshToken": "new_refresh_token_here", 
    "expiresIn": "90000"
  }
}
```

### Task Creation Success
```json
{
  "statusCode": null,
  "httpStatusCode": 201,
  "description": null,
  "clientMessageId": null,
  "timestamp": 1704096000000,
  "data": {
    "status": "SUCCESS",
    "message": "Task created successfully",
    "details": {
      "id": "uuid",
      "name": "ZNS_TOKEN_REFRESH",
      "group": "ZNS_GROUP",
      "cronExpression": "*/10 * * * * ?",
      "jobType": "ZNS_TOKEN_REFRESH",
      "isActive": false,
      "createdAt": 1704096000000,
      "updatedAt": 1704096000000,
      "createdBy": "system",
      "updatedBy": "system"
    }
  }
}
```

### Task Scheduled Success
```json
{
  "statusCode": null,
  "httpStatusCode": 200,
  "description": null,
  "clientMessageId": null,
  "timestamp": 1704096300000,
  "data": {
    "status": "SUCCESS",
    "message": "Task scheduled successfully",
    "details": {
      "id": "uuid",
      "name": "ZNS_TOKEN_REFRESH",
      "group": "ZNS_GROUP",
      "cronExpression": "*/10 * * * * ?",
      "jobType": "ZNS_TOKEN_REFRESH",
      "isActive": true,
      "createdAt": 1704096000000,
      "updatedAt": 1704096300000,
      "createdBy": "system",
      "updatedBy": "admin"
    }
  }
}
```

### Get All Tasks with Pagination Success
```json
{
  "statusCode": null,
  "httpStatusCode": 200,
  "description": null,
  "clientMessageId": null,
  "timestamp": 1704096000000,
  "data": {
    "content": [
      {
        "id": "uuid1",
        "name": "ZNS_TOKEN_REFRESH",
        "group": "ZNS_GROUP",
        "cronExpression": "*/10 * * * * ?",
        "jobType": "ZNS_TOKEN_REFRESH",
        "isActive": true,
        "createdAt": 1704096000000,
        "updatedAt": 1704096300000,
        "createdBy": "system",
        "updatedBy": "admin"
      }
    ],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10,
      "sort": {
        "sorted": true,
        "empty": false,
        "unsorted": false
      },
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "totalElements": 1,
    "totalPages": 1,
    "last": true,
    "first": true,
    "size": 10,
    "number": 0,
    "sort": {
      "sorted": true,
      "empty": false,
      "unsorted": false
    },
    "numberOfElements": 1,
    "empty": false
  }
}
```

### Get All Tasks (No Pagination) Success
```json
{
  "statusCode": null,
  "httpStatusCode": 200,
  "description": null,
  "clientMessageId": null,
  "timestamp": 1704096000000,
  "data": [
    {
      "id": "uuid1",
      "name": "ZNS_TOKEN_REFRESH",
      "group": "ZNS_GROUP",
      "cronExpression": "*/10 * * * * ?",
      "jobType": "ZNS_TOKEN_REFRESH",
      "isActive": true,
      "createdAt": 1704096000000,
      "updatedAt": 1704096300000,
      "createdBy": "system",
      "updatedBy": "admin"
    }
  ]
}
```

## Authentication

### Keycloak Configuration:
Job Schedule service đã được cấu hình với Keycloak authentication nhất quán với các service khác:
- **Realm**: `pronexus_dev`
- **Auth Server**: `https://identity.pronexus.vn` (dev/uat) hoặc `https://auth.pronexus.vn` (live)
- **Client ID**: `pronexus_dev`
- **Client Secret**: `m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2`

### Lấy Access Token:
```bash
curl -X POST https://identity.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials" \
  -d "client_id=pronexus_dev" \
  -d "client_secret=m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2"
```

## Testing

Import Postman collection: `user/ZNS_Token_Refresh.postman_collection.json`

### Features:
- **Auto-authentication**: Collection tự động lấy token từ Keycloak
- **Bearer token**: Tự động thêm vào header của mọi request
- **Token refresh**: Tự động refresh khi token hết hạn

### Test Variables:
- `base_url`: https://api.pronexus.vn/user
- `job_base_url`: https://api.pronexus.vn/job
- `keycloak_url`: https://identity.pronexus.vn
- `realm`: pronexus_dev
- `client_id`: pronexus_dev
- `client_secret`: m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2

## Monitoring

Kiểm tra logs để theo dõi:
- User Service: `ZnsTokenServiceImpl` logs
- Job Schedule: `ZnsTokenRefreshJob` logs
- Database: Cập nhật `ZNS_ACCESS_TOKEN` trong bảng configurations

## Troubleshooting

### Lỗi thường gặp:
1. **401 Unauthorized**: Cần access token từ Keycloak - xem phần Authentication
2. **Syntax error at "group"**: Reserved keyword issue - đã khắc phục bằng escape quotes
3. **Không tìm thấy ZNS_FRESH_TOKEN**: Cần thêm refresh token vào database
4. **Feign connection error**: Kiểm tra network và URL configuration
5. **Job không chạy**: Kiểm tra Quartz scheduler và database connection

### Database Schema:
Bảng `tasks` sẽ được tự động tạo bởi Hibernate với `ddl-auto=update`:
- Sử dụng `AuditableEntity` từ common-library
- Tự động tracking `createdAt`, `updatedAt`, `createdBy`, `updatedBy`
- Audit fields sử dụng Long timestamp (epoch milliseconds)
- Schema: `public` với `globally_quoted_identifiers=true`
- Field `group` được escape để tránh reserved keyword conflict
- Nếu auto-creation fail, chạy `job-schedule/manual_table_creation.sql`

### Debug commands:
```bash
# Kiểm tra tất cả tasks
curl https://api.pronexus.vn/job/tasks

# Kiểm tra task cụ thể
curl https://api.pronexus.vn/job/tasks/{task_id}

# Kiểm tra configuration
SELECT * FROM portal.configurations WHERE config_key IN ('ZNS_ACCESS_TOKEN', 'ZNS_FRESH_TOKEN');

# Kiểm tra tasks trong database
SELECT id, name, job_type, is_active, cron_expression, created_at, updated_at, created_by, updated_by FROM tasks;
```

## Lợi ích của thiết kế mới

1. **Tái sử dụng**: Có thể tạo nhiều task cùng loại với cron expression khác nhau
2. **Linh hoạt**: Start/stop job mà không mất thông tin task
3. **Quản lý tốt**: Xem được trạng thái tất cả tasks
4. **Mở rộng**: Dễ dàng thêm loại job mới
5. **Debug**: Có thể test từng bước một cách độc lập
