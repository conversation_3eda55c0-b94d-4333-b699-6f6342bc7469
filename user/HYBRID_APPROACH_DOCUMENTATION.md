# Hybrid Approach for User Deletion

## 🏗️ **KIẾN TRÚC HYBRID**

Hybrid approach kết hợp ưu điểm của cả SQL Transaction và Service Calls để đảm bảo tính consistency và flexibility.

## 🔄 **4 PHASES DELETION PROCESS**

### **PHASE 1: Local Database (ACID Guaranteed)**
```java
@Transactional
private void deleteUserDataInSameDatabase(String userId, List<String> deletionLog) {
    // Xóa OTP trong User service database
    // ✅ ACID compliance
    // ✅ Rollback tự động nếu lỗi
}
```

### **PHASE 2: External Services (Error Handling)**
```java
private void deleteUserDataInExternalServices(String userId, List<String> deletionLog, List<Exception> errors) {
    // Portal Service calls
    deleteEmployeeLink(userId, deletionLog, errors);
    deletePartnerLink(userId, deletionLog, errors);
    deleteUserDevicesData(userId, deletionLog, errors);
    deleteSalaryAdvanceLimitData(userId, deletionLog, errors);
    
    // Salary Advance Service calls
    deleteSalaryAdvanceData(userId, deletionLog, errors);
    
    // ✅ Graceful degradation
    // ✅ Continue on partial failures
}
```

### **PHASE 3: Keycloak (Critical - Must Succeed)**
```java
private void deleteUserFromKeycloak(String userId, List<String> deletionLog, List<Exception> errors) {
    // Xóa user khỏi Keycloak
    // ❌ Throw exception nếu fail (critical)
    // ✅ Cuối cùng vì không thể rollback
}
```

### **PHASE 4: Result Handling**
```java
private CommandResponse<Void> handleDeletionResult(String userId, boolean force, 
        List<String> deletionLog, List<Exception> errors) {
    // Phân tích kết quả
    // Tạo response message chi tiết
    // Quyết định success/failure
}
```

## 📊 **LOGGING SYSTEM**

### **Emoji-based Status Indicators:**
- ✅ **Success**: Operation thành công
- ⚠️ **Warning**: Operation thất bại nhưng không critical
- ℹ️ **Info**: Không có dữ liệu để xử lý
- ❌ **Error**: Critical failure

### **Sample Log Output:**
```
=== DELETION SUMMARY FOR USER 123 ===
Total operations: 7
Successful operations: 5
Warning operations: 1
Info operations: 1
Total errors: 1
Errors encountered:
- FeignException: Connection timeout to portal service
Deletion log:
- ✅ Đã xóa 3 OTP (local DB)
- ✅ Đã xóa liên kết với nhân viên: Nguyễn Văn A
- ℹ️ Không có liên kết partner để xóa
- ✅ Đã xóa 2 thiết bị của user
- ⚠️ Cảnh báo: Không thể xóa salary advance limit
- ✅ Đã xóa tất cả dữ liệu salary advance của employee
- ✅ Đã xóa user khỏi Keycloak
=== END DELETION SUMMARY ===
```

## 🎯 **ERROR HANDLING STRATEGY**

### **Local Database Errors:**
- **Behavior**: Throw exception → Rollback transaction
- **Rationale**: Core data must be consistent

### **External Service Errors:**
- **Behavior**: Log error → Continue with other operations
- **Rationale**: Partial success better than total failure

### **Keycloak Errors:**
- **Behavior**: Throw exception → Mark as failure
- **Rationale**: User must be deleted from auth system

## 📋 **RESPONSE MESSAGE FORMAT**

### **Success Response:**
```json
{
  "success": true,
  "data": null,
  "message": "✅ Xóa user thành công (force=true). Thống kê: 6 thành công, 0 cảnh báo, 1 thông tin. Chi tiết: ✅ Đã xóa 3 OTP (local DB); ℹ️ Không có liên kết partner để xóa; ✅ Đã xóa 2 thiết bị của user; ✅ Đã xóa user khỏi Keycloak"
}
```

### **Warning Response:**
```json
{
  "success": true,
  "data": null,
  "message": "⚠️ Xóa user hoàn thành với cảnh báo (force=true). Thống kê: 5 thành công, 1 cảnh báo, 1 thông tin. Chi tiết: ✅ Đã xóa 3 OTP (local DB); ⚠️ Cảnh báo: Không thể xóa salary advance limit; ✅ Đã xóa user khỏi Keycloak"
}
```

### **Failure Response:**
```json
{
  "success": false,
  "data": null,
  "message": "❌ Lỗi nghiêm trọng khi xóa user: Không thể xóa user khỏi Keycloak: Connection refused"
}
```

## 🔧 **CONFIGURATION**

### **Retry Mechanism (Future Enhancement):**
```java
@Retryable(value = {FeignException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
private void deleteEmployeeLink(String userId, List<String> deletionLog, List<Exception> errors) {
    // Implementation with retry
}
```

### **Circuit Breaker (Future Enhancement):**
```java
@CircuitBreaker(name = "portal-service", fallbackMethod = "fallbackDeleteEmployee")
private void deleteEmployeeLink(String userId, List<String> deletionLog, List<Exception> errors) {
    // Implementation with circuit breaker
}
```

## ✅ **BENEFITS OF HYBRID APPROACH**

### **1. Data Consistency:**
- Local data (OTP) → ACID guaranteed
- External data → Best effort with graceful degradation

### **2. Reliability:**
- Continue operation even if some services fail
- Clear error reporting and logging

### **3. Maintainability:**
- Modular design with separate methods for each service
- Easy to add new services or modify existing logic

### **4. Observability:**
- Detailed logging with emoji indicators
- Clear success/warning/failure categorization
- Comprehensive error tracking

### **5. User Experience:**
- Informative response messages
- Partial success better than total failure
- Clear indication of what succeeded/failed

## 🚀 **FUTURE ENHANCEMENTS**

1. **Compensation Transactions**: Rollback external changes if Keycloak fails
2. **Async Processing**: Queue failed operations for retry
3. **Metrics**: Track success rates per service
4. **Alerting**: Notify admins of critical failures
5. **Audit Trail**: Store deletion history for compliance
