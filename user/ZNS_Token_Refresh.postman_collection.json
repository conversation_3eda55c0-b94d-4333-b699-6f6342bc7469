{"info": {"_postman_id": "zns-token-refresh-collection", "name": "ZNS Token Refresh API", "description": "Collection để test API refresh ZNS token", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Refresh ZNS Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/otp/refresh-token", "host": ["{{base_url}}"], "path": ["api", "v1", "otp", "refresh-token"]}}, "response": []}, {"name": "Create ZNS Token Refresh Job", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{job_base_url}}/tasks/zns-token-refresh", "host": ["{{job_base_url}}"], "path": ["tasks", "zns-token-refresh"]}}, "response": []}], "variable": [{"key": "base_url", "value": "https://api.pronexus.vn/user", "type": "string"}, {"key": "job_base_url", "value": "https://api.pronexus.vn/job", "type": "string"}]}