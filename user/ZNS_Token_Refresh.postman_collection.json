{"info": {"_postman_id": "zns-token-refresh-collection", "name": "ZNS Token Refresh API", "description": "Collection để test API refresh ZNS token với Keycloak authentication", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "item": [{"name": "User Service", "item": [{"name": "Refresh ZNS Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v1/otp/refresh-token", "host": ["{{base_url}}"], "path": ["api", "v1", "otp", "refresh-token"]}}, "response": []}]}, {"name": "Job Schedule Service", "item": [{"name": "Get All Tasks (Paginated)", "request": {"method": "GET", "header": [], "url": {"raw": "{{job_base_url}}/tasks?page=0&size=10&sort=createdAt,desc", "host": ["{{job_base_url}}"], "path": ["tasks"], "query": [{"key": "page", "value": "0", "description": "Page number (0-based)"}, {"key": "size", "value": "10", "description": "Page size"}, {"key": "sort", "value": "createdAt,desc", "description": "Sort by field and direction"}, {"key": "name", "value": "", "description": "Filter by task name", "disabled": true}, {"key": "jobType", "value": "", "description": "Filter by job type", "disabled": true}, {"key": "isActive", "value": "", "description": "Filter by active status", "disabled": true}]}}, "response": []}, {"name": "Get All Tasks (No Pagination)", "request": {"method": "GET", "header": [], "url": {"raw": "{{job_base_url}}/tasks/all", "host": ["{{job_base_url}}"], "path": ["tasks", "all"]}}, "response": []}, {"name": "Get Task by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{job_base_url}}/tasks/{{task_id}}", "host": ["{{job_base_url}}"], "path": ["tasks", "{{task_id}}"]}}, "response": []}, {"name": "Create ZNS Token Refresh Task (Only DB)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{job_base_url}}/tasks/zns-token-refresh", "host": ["{{job_base_url}}"], "path": ["tasks", "zns-token-refresh"]}}, "response": []}, {"name": "Create and Schedule ZNS Token Refresh Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{job_base_url}}/tasks/zns-token-refresh/schedule", "host": ["{{job_base_url}}"], "path": ["tasks", "zns-token-refresh", "schedule"]}}, "response": []}, {"name": "Schedule Task by ID", "request": {"method": "POST", "header": [], "url": {"raw": "{{job_base_url}}/tasks/{{task_id}}/schedule", "host": ["{{job_base_url}}"], "path": ["tasks", "{{task_id}}", "schedule"]}}, "response": []}, {"name": "Unschedule Task by ID", "request": {"method": "POST", "header": [], "url": {"raw": "{{job_base_url}}/tasks/{{task_id}}/unschedule", "host": ["{{job_base_url}}"], "path": ["tasks", "{{task_id}}", "unschedule"]}}, "response": []}, {"name": "Create Custom Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"CUSTOM_TASK\",\n    \"group\": \"CUSTOM_GROUP\",\n    \"cronExpression\": \"0 */5 * * * ?\",\n    \"jobType\": \"TASK_JOB\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{job_base_url}}/tasks", "host": ["{{job_base_url}}"], "path": ["tasks"]}}, "response": []}, {"name": "Delete Task", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{job_base_url}}/tasks/{{task_id}}", "host": ["{{job_base_url}}"], "path": ["tasks", "{{task_id}}"]}}, "response": []}]}], "variable": [{"key": "base_url", "value": "https://api.pronexus.vn/user", "type": "string"}, {"key": "job_base_url", "value": "https://api.pronexus.vn/job", "type": "string"}, {"key": "task_id", "value": "", "type": "string", "description": "Task ID để test schedule/unschedule"}, {"key": "access_token", "value": "", "type": "string", "description": "Keycloak access token for authentication"}, {"key": "keycloak_url", "value": "https://identity.pronexus.vn", "type": "string", "description": "Keycloak server URL"}, {"key": "realm", "value": "pronexus_dev", "type": "string", "description": "Keycloak realm"}, {"key": "client_id", "value": "pronexus_dev", "type": "string", "description": "Keycloak client ID"}, {"key": "client_secret", "value": "m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2", "type": "string", "description": "Keycloak client secret"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-refresh token if needed", "if (!pm.globals.get('access_token') || pm.globals.get('token_expires') < Date.now()) {", "    // Get token from Keycloak", "    const tokenRequest = {", "        url: pm.variables.get('keycloak_url') + '/realms/' + pm.variables.get('realm') + '/protocol/openid-connect/token',", "        method: 'POST',", "        header: {", "            'Content-Type': 'application/x-www-form-urlencoded'", "        },", "        body: {", "            mode: 'urlencoded',", "            urlencoded: [", "                {key: 'grant_type', value: 'client_credentials'},", "                {key: 'client_id', value: pm.variables.get('client_id')},", "                {key: 'client_secret', value: pm.variables.get('client_secret')}", "            ]", "        }", "    };", "    ", "    pm.sendRequest(tokenRequest, function (err, response) {", "        if (err) {", "            console.log('Error getting token:', err);", "        } else {", "            const jsonData = response.json();", "            pm.globals.set('access_token', jsonData.access_token);", "            pm.globals.set('token_expires', Date.now() + (jsonData.expires_in * 1000));", "            pm.variables.set('access_token', jsonData.access_token);", "        }", "    });", "}"]}}]}