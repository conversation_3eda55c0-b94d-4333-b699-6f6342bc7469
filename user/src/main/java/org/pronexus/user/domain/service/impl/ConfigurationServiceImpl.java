package org.pronexus.user.domain.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.domain.entity.Configuration;
import org.pronexus.user.domain.entity.constants.ConfigurationStatus;
import org.pronexus.user.domain.repository.ConfigurationRepository;
import org.pronexus.user.domain.service.core.ConfigurationService;
import org.pronexus.user.domain.entity.data.JsonMap;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class ConfigurationServiceImpl implements ConfigurationService {

    private final ConfigurationRepository configurationRepository;

    @Override
    @Transactional(readOnly = true)
    public Optional<String> getConfigurationValue(String key) {
        try {
            Optional<Configuration> configOpt = configurationRepository.findByKeyAndStatus(key, ConfigurationStatus.ACTIVE);
            
            if (configOpt.isPresent()) {
                Configuration config = configOpt.get();
                if (config.getValue() != null) {
                    String value = config.getValue().getString("value");
                    log.debug("Lấy cấu hình thành công: key={}, value={}", key, value);
                    return Optional.ofNullable(value);
                }
            }
            
            log.warn("Không tìm thấy cấu hình với key: {}", key);
            return Optional.empty();
        } catch (Exception e) {
            log.error("Lỗi khi lấy cấu hình với key {}: {}", key, e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    @Transactional
    public boolean updateConfigurationValue(String key, String value) {
        try {
            Optional<Configuration> configOpt = configurationRepository.findByKeyAndStatus(key, ConfigurationStatus.ACTIVE);

            if (configOpt.isPresent()) {
                Configuration config = configOpt.get();

                // Tạo JsonMap mới với giá trị cập nhật
                JsonMap jsonValue = new JsonMap();
                jsonValue.set("value", value);
                config.setValue(jsonValue);

                configurationRepository.save(config);
                log.info("Cập nhật cấu hình thành công: key={}, value={}", key, value);
                return true;
            } else {
                log.warn("Không tìm thấy cấu hình với key: {}", key);
                return false;
            }
        } catch (Exception e) {
            log.error("Lỗi khi cập nhật cấu hình với key {}: {}", key, e.getMessage(), e);
            return false;
        }
    }
}
