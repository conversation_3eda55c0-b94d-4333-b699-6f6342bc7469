package org.pronexus.user.domain.service.core;

import java.util.Optional;

public interface ConfigurationService {

    /**
     * L<PERSON>y giá trị cấu hình theo key
     * @param key Configuration key
     * @return <PERSON><PERSON><PERSON> trị cấu hình nếu tìm thấy
     */
    Optional<String> getConfigurationValue(String key);

    /**
     * Cập nhật giá trị cấu hình theo key
     * @param key Configuration key
     * @param value Giá trị mới
     * @return true nếu cập nhật thành công
     */
    boolean updateConfigurationValue(String key, String value);
}
