package org.pronexus.user.domain.service.impl;

import com.salaryadvance.commonlibrary.rest.CommandResponse;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.app.dtos.zns.RefreshTokenResponseDto;
import org.pronexus.user.domain.feign.ZaloOAuthFeignClient;
import org.pronexus.user.domain.service.core.ConfigurationService;
import org.pronexus.user.domain.service.core.ZnsTokenService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Implementation của ZnsTokenService
 * Dùng để quản lý token của Zalo Notification Service (ZNS)
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ZnsTokenServiceImpl implements ZnsTokenService {

    private final ZaloOAuthFeignClient zaloOAuthFeignClient;
    private final ConfigurationService configurationService;

    @Value("${zns.app.id}")
    private String appId;

    @Value("${zns.secret.key:SG5T7VSWI9q8L34CGE2t}")
    private String secretKey;

    @Override
    public CommandResponse<RefreshTokenResponseDto> refreshAccessToken() {
        try {
            // Lấy refresh token từ database
            String refreshToken = configurationService.getConfigurationValue("ZNS_FRESH_TOKEN")
                    .orElseThrow(() -> new RuntimeException("Không tìm thấy ZNS_FRESH_TOKEN trong cấu hình"));

            // Tạo request body
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("refresh_token", refreshToken);
            requestBody.put("app_id", appId);
            requestBody.put("grant_type", "refresh_token");

            // Gọi API refresh token
            RefreshTokenResponseDto response = zaloOAuthFeignClient.refreshAccessToken(secretKey, requestBody);

            // Cập nhật access token mới vào database
            boolean updated = configurationService.updateConfigurationValue("ZNS_ACCESS_TOKEN", response.getAccessToken());
            
            if (!updated) {
                log.error("Không thể cập nhật ZNS_ACCESS_TOKEN vào database");
                return CommandResponse.failure(null, "Không thể cập nhật access token vào database");
            }

            log.info("Refresh ZNS access token thành công");
            return CommandResponse.success(response, "Refresh token thành công");

        } catch (FeignException e) {
            log.error("Lỗi khi gọi API refresh token: {}", e.getMessage(), e);
            return CommandResponse.failure(null, "Lỗi khi gọi API refresh token: " + e.getMessage());
        } catch (Exception e) {
            log.error("Lỗi không xác định khi refresh token: {}", e.getMessage(), e);
            return CommandResponse.failure(null, "Lỗi không xác định: " + e.getMessage());
        }
    }
}
