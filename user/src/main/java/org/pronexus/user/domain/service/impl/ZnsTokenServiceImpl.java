package org.pronexus.user.domain.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.app.dtos.zns.RefreshTokenResponseDto;
import org.pronexus.user.domain.feign.ZaloOAuthFeignClient;
import org.pronexus.user.domain.service.core.ConfigurationService;
import org.pronexus.user.domain.service.core.ZnsTokenService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
 * Implementation của ZnsTokenService
 * Dùng để quản lý token của Zalo Notification Service (ZNS)
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ZnsTokenServiceImpl implements ZnsTokenService {

    private final ZaloOAuthFeignClient zaloOAuthFeignClient;
    private final ConfigurationService configurationService;
    private final ObjectMapper objectMapper;

    @Value("${zns.app.id}")
    private String appId;

    @Value("${zns.secret.key:SG5T7VSWI9q8L34CGE2t}")
    private String secretKey;

    @Override
    public CommandResponse<RefreshTokenResponseDto> refreshAccessToken() {
        try {
            // Lấy refresh token từ database
            String refreshToken = configurationService.getConfigurationValue("ZNS_FRESH_TOKEN")
                    .orElseThrow(() -> new RuntimeException("Không tìm thấy ZNS_FRESH_TOKEN trong cấu hình"));

            // Tạo request body với MultiValueMap để tránh reflection issues
            MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
            requestBody.add("refresh_token", refreshToken);
            requestBody.add("app_id", appId);
            requestBody.add("grant_type", "refresh_token");

            // Gọi API refresh token với fallback mechanism
            RefreshTokenResponseDto response = callZaloRefreshTokenApi(secretKey, requestBody);

            // Cập nhật access token mới vào database
            boolean updated = configurationService.updateConfigurationValue("ZNS_ACCESS_TOKEN", response.getAccessToken());
            
            if (!updated) {
                log.error("Không thể cập nhật ZNS_ACCESS_TOKEN vào database");
                return CommandResponse.failure(null, "Không thể cập nhật access token vào database");
            }

            log.info("Refresh ZNS access token thành công");
            return CommandResponse.success(response, "Refresh token thành công");

        } catch (FeignException e) {
            log.error("Lỗi khi gọi API refresh token: {}", e.getMessage(), e);
            return CommandResponse.failure(null, "Lỗi khi gọi API refresh token: " + e.getMessage());
        } catch (Exception e) {
            log.error("Lỗi không xác định khi refresh token: {}", e.getMessage(), e);
            return CommandResponse.failure(null, "Lỗi không xác định: " + e.getMessage());
        }
    }

    /**
     * Gọi Zalo refresh token API với fallback mechanism
     */
    private RefreshTokenResponseDto callZaloRefreshTokenApi(String secretKey, MultiValueMap<String, String> requestBody) {
        try {
            // Thử gọi API với auto-decode trước
            log.debug("Attempting to call Zalo API with auto-decode");
            return zaloOAuthFeignClient.refreshAccessToken(secretKey, requestBody);

        } catch (Exception e) {
            log.warn("Auto-decode failed, trying manual parsing: {}", e.getMessage());

            try {
                // Fallback: gọi API trả về String và manual parse
                String responseString = zaloOAuthFeignClient.refreshAccessTokenAsString(secretKey, requestBody);
                log.debug("Received response string: {}", responseString);

                // Parse JSON string thành DTO
                RefreshTokenResponseDto response = objectMapper.readValue(responseString, RefreshTokenResponseDto.class);
                log.info("Successfully parsed response with manual parsing");
                return response;

            } catch (Exception parseException) {
                log.error("Both auto-decode and manual parsing failed", parseException);
                throw new RuntimeException("Failed to parse Zalo API response", parseException);
            }
        }
    }
}
