package org.pronexus.user.domain.service.core;

import com.salaryadvance.commonlibrary.rest.CommandResponse;
import org.pronexus.user.app.dtos.zns.RefreshTokenResponseDto;

/**
 * Interface định nghĩa các phương thức của ZnsTokenService
 * Dùng để quản lý token của Zalo Notification Service (ZNS)
 */
public interface ZnsTokenService {

    /**
     * Refresh access token từ Zalo OAuth API
     * 
     * @return CommandResponse chứa thông tin token mới
     */
    CommandResponse<RefreshTokenResponseDto> refreshAccessToken();
}
