package org.pronexus.user.domain.feign;

import org.pronexus.user.app.dtos.zns.RefreshTokenResponseDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * Feign client để gọi API OAuth của Zalo
 */
@FeignClient(name = "zalo-oauth", url = "https://oauth.zaloapp.com")
public interface ZaloOAuthFeignClient {

    /**
     * Refresh access token từ Zalo OAuth API
     * 
     * @param secretKey Secret key của app
     * @param request Request body chứa refresh_token, app_id, grant_type
     * @return Response chứa access_token và refresh_token mới
     */
    @PostMapping(value = "/v4/oa/access_token", 
                 consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE,
                 produces = MediaType.APPLICATION_JSON_VALUE)
    RefreshTokenResponseDto refreshAccessToken(
            @RequestHeader("secret_key") String secretKey,
            @RequestBody Map<String, String> request
    );
}
