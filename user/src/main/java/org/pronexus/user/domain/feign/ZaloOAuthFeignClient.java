package org.pronexus.user.domain.feign;

import org.pronexus.user.app.dtos.zns.RefreshTokenResponseDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * Feign client để gọi API OAuth của Zalo
 */
@FeignClient(name = "zalo-oauth", url = "https://oauth.zaloapp.com", configuration = org.pronexus.user.config.FeignConfig.class)
public interface ZaloOAuthFeignClient {

    /**
     * Refresh access token từ Zalo OAuth API
     *
     * @param secretKey Secret key của app
     * @param request Request body chứa refresh_token, app_id, grant_type
     * @return Response chứa access_token và refresh_token mới
     */
    @PostMapping(value = "/v4/oa/access_token",
                 consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE,
                 produces = MediaType.APPLICATION_JSON_VALUE)
    RefreshTokenResponseDto refreshAccessToken(
            @RequestHeader("secret_key") String secretKey,
            @RequestBody MultiValueMap<String, String> request
    );

    /**
     * Alternative method - trả về String để manual parsing nếu auto-decode fail
     */
    @PostMapping(value = "/v4/oa/access_token",
                 consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String refreshAccessTokenAsString(
            @RequestHeader("secret_key") String secretKey,
            @RequestBody MultiValueMap<String, String> request
    );
}
