package org.pronexus.user.domain.feign.adapter;

import com.salaryadvance.commonlibrary.rest.Response;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.domain.feign.SalaryAdvanceFeignClient;
import org.pronexus.user.domain.feign.adapter.KeycloakClient;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class SalaryAdvanceClient {

    private final SalaryAdvanceFeignClient salaryAdvanceFeignClient;
    private final KeycloakClient keycloakClient;

    /**
     * Kiể<PERSON> tra số lượng dữ liệu salary advance của employee
     *
     * @param employeeId ID của employee
     * @return Map với key là tên bảng và value là số lượng record
     */
    public java.util.Map<String, Long> getEmployeeDataCount(String employeeId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<java.util.Map<String, Long>> response = salaryAdvanceFeignClient.getEmployeeDataCount(token,
                    employeeId);
            return response.getData() != null ? response.getData() : java.util.Collections.emptyMap();
        } catch (FeignException e) {
            log.error("[GET SALARY_ADVANCE_DATA_COUNT] Cannot get salary advance data count for employee {}: {}",
                    employeeId, e.getMessage(), e);
            return java.util.Collections.emptyMap();
        }
    }

    /**
     * Xóa tất cả dữ liệu salary advance của employee
     *
     * @param employeeId ID của employee
     * @return true nếu thành công
     */
    public boolean deleteAllEmployeeData(String employeeId) {
        String token = "Bearer " + keycloakClient.getClientToken();
        try {
            Response<Boolean> response = salaryAdvanceFeignClient.deleteAllEmployeeData(token, employeeId);
            return response.getData() != null && response.getData();
        } catch (FeignException e) {
            log.error("[DELETE SALARY_ADVANCE_DATA] Cannot delete salary advance data for employee {}: {}",
                    employeeId, e.getMessage(), e);
            return false;
        }
    }
}
