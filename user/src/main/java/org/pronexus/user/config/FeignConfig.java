package org.pronexus.user.config;

import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.form.FormEncoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Configuration cho Feign clients
 */
@Configuration
public class FeignConfig {

    /**
     * Custom decoder để handle text/json content type từ Zalo API
     */
    @Bean
    public Decoder feignDecoder() {
        // Tạo Jackson converter với support cho text/json
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter();
        
        // Thêm text/json vào supported media types
        List<MediaType> supportedMediaTypes = new ArrayList<>(jacksonConverter.getSupportedMediaTypes());
        supportedMediaTypes.add(new MediaType("text", "json"));
        supportedMediaTypes.add(new MediaType("text", "json", java.nio.charset.StandardCharsets.UTF_8));
        jacksonConverter.setSupportedMediaTypes(supportedMediaTypes);
        
        // Tạo ObjectFactory cho HttpMessageConverters
        ObjectFactory<HttpMessageConverters> messageConverters = () -> 
            new HttpMessageConverters(jacksonConverter);
        
        return new ResponseEntityDecoder(new SpringDecoder(messageConverters));
    }

    /**
     * Custom encoder để handle form data
     */
    @Bean
    public Encoder feignEncoder() {
        // Tạo Jackson converter
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter();
        
        // Tạo ObjectFactory cho HttpMessageConverters
        ObjectFactory<HttpMessageConverters> messageConverters = () -> 
            new HttpMessageConverters(jacksonConverter);
        
        // Sử dụng FormEncoder để support form data
        return new FormEncoder(new SpringEncoder(messageConverters));
    }
}
