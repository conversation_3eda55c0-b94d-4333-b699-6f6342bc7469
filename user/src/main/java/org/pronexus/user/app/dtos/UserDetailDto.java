package org.pronexus.user.app.dtos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.keycloak.representations.idm.MappingsRepresentation;
import org.keycloak.representations.idm.UserRepresentation;

/**
 * DTO chứa thông tin chi tiết của user bao gồm statistics
 * Dùng cho API getUserDetail
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDetailDto {
    
    /**
     * Thông tin user từ Keycloak
     */
    private UserRepresentation user;
    
    /**
     * Role mapping của user
     */
    private MappingsRepresentation roles;
    
    /**
     * Thống kê dữ liệu liên quan của user
     * Chỉ tính khi get detail để tránh performance issue ở list
     */
    private UserStatisticsDto statistics;
    
    /**
     * Thông tin profile từ Portal service (nếu có)
     */
    private UserProfileInfo profile;
    
    /**
     * Thông tin bổ sung về user
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserProfileInfo {
        /**
         * Thông tin employee (nếu có)
         */
        private EmployeeInfo employee;
        
        /**
         * Thông tin partner (nếu có)
         */
        private PartnerInfo partner;
        
        /**
         * Danh sách thiết bị
         */
        private java.util.List<DeviceInfo> devices;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmployeeInfo {
        private Long id;
        private String name;
        private String email;
        private String code;
        private Long partnerId;
        private String partnerName;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PartnerInfo {
        private Long id;
        private String name;
        private String code;
        private String email;
        private String phone;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeviceInfo {
        private Integer id;
        private String deviceId;
        private String deviceName;
        private String platform;
        private String status;
        private java.time.LocalDateTime createdAt;
        private java.time.LocalDateTime lastActiveAt;
    }
}
