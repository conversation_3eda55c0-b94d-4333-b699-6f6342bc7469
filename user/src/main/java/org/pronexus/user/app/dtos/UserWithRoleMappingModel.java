package org.pronexus.user.app.dtos;

import lombok.Data;
import org.keycloak.representations.idm.MappingsRepresentation;
import org.keycloak.representations.idm.UserRepresentation;

@Data
public class UserWithRoleMappingModel {
    private UserRepresentation user;
    private MappingsRepresentation roles;

    /**
     * Thống kê dữ liệu liên quan của user
     * Dùng để hiển thị UI cho admin quyết định xóa user
     */
    private UserStatisticsDto statistics;
}
