package org.pronexus.user.app.controllers;

import com.salaryadvance.commonlibrary.exception.base.DataValidationException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.rest.Response;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.pronexus.user.app.dtos.otp.GenerateOtpRequestDto;
import org.pronexus.user.app.dtos.otp.GenerateOtpResponseDto;
import org.pronexus.user.app.dtos.otp.VerifyOtpRequestDto;
import org.pronexus.user.app.dtos.otp.VerifyOtpResponseDto;
import org.pronexus.user.app.dtos.zns.RefreshTokenResponseDto;
import org.pronexus.user.domain.entity.OtpDeliveryMethod;
import org.pronexus.user.domain.entity.OtpType;
import org.pronexus.user.domain.service.core.OtpService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;

/**
 * Controller xử lý các API liên quan đến OTP
 * Các API trong controller này không yêu cầu xác thực
 */
@RestController
@RequestMapping("/api/v1/otp")
@RequiredArgsConstructor
@Slf4j
@Validated
public class OtpController {

    private final OtpService otpService;

    /**
     * API tạo mã OTP mới
     *
     * @param requestDto Thông tin yêu cầu tạo OTP
     * @return Thông tin OTP đã tạo0968
     */
    @PostMapping("/generate")
    public ResponseEntity<Response<CommandResponse<GenerateOtpResponseDto>>> generateOtp(
            @Valid @RequestBody GenerateOtpRequestDto requestDto) {
        log.info("Yêu cầu tạo OTP: {}", requestDto);
        CommandResponse<GenerateOtpResponseDto> response = otpService.generateOtp(requestDto);
        return Response.success(response);
    }

    /**
     * API xác thực mã OTP
     *
     * @param requestDto Thông tin yêu cầu xác thực OTP
     * @return Kết quả xác thực
     */
    @PostMapping("/verify")
    public ResponseEntity<Response<CommandResponse<VerifyOtpResponseDto>>> verifyOtp(
            @Valid @RequestBody VerifyOtpRequestDto requestDto,
            @RequestParam(required = false, defaultValue = "true") boolean updateStatus) {

        // Xử lý thông thường
        CommandResponse<VerifyOtpResponseDto> response = otpService.verifyOtp(requestDto, updateStatus);
        return Response.success(response);
    }


    /**
     * API vô hiệu hóa OTP theo ID (chỉ dành cho admin)
     *
     * @param otpId ID của OTP
     * @return Kết quả vô hiệu hóa
     */
    @DeleteMapping("/{otpId}")
    public ResponseEntity<Response<CommandResponse<Void>>> invalidateOtp(@PathVariable Long otpId) {
        log.info("Yêu cầu vô hiệu hóa OTP ID: {}", otpId);
        CommandResponse<Void> response = otpService.invalidateOtp(otpId);
        return Response.success(response);
    }

    /**
     * API kiểm tra mã OTP mới nhất theo số điện thoại và loại OTP (chỉ dùng cho mục đích test)
     * //TODO: Xóa khi hoàn thành test
     *
     * @param contactInfo Số điện thoại
     * @param type Loại OTP
     * @return Mã OTP
     */
    @GetMapping("/test/check")
    public ResponseEntity<Response<CommandResponse<String>>> getLatestOtpForTesting(
            @RequestParam String contactInfo,
            @RequestParam OtpType type) {
        log.info("Yêu cầu kiểm tra mã OTP mới nhất cho mục đích test, contactInfo: {}, type: {}", contactInfo, type);
        CommandResponse<String> response = otpService.getLatestOtpForTesting(contactInfo, type);
        return Response.success(response);
    }

    /**
     * API tạo mã OTP qua Zalo ZNS
     *
     * @param type Loại OTP
     * @param phoneNumber Số điện thoại
     * @param templateId ID của template ZNS (tùy chọn)
     * @return Thông tin OTP đã tạo
     */
    @PostMapping("/zns")
    public ResponseEntity<Response<CommandResponse<GenerateOtpResponseDto>>> generateZnsOtp(
            @RequestParam OtpType type,
            @RequestParam String phoneNumber,
            @RequestParam(required = false) String templateId) {
        log.info("Yêu cầu tạo OTP qua ZNS loại {} đến số điện thoại {}", type, phoneNumber);

        GenerateOtpRequestDto requestDto = GenerateOtpRequestDto.builder()
                .type(type)
                .contactInfo(phoneNumber)
                .deliveryMethod(OtpDeliveryMethod.ZNS)
                .templateId(templateId)
                .build();

        CommandResponse<GenerateOtpResponseDto> response = otpService.generateOtp(requestDto);
        return Response.success(response);
    }

}
