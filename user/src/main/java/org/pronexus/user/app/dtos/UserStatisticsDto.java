package org.pronexus.user.app.dtos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO chứa thống kê dữ liệu liên quan của user
 * Dùng để hiển thị UI cho admin quyết định có nên xóa user hay không
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserStatisticsDto {
    
    // =====================================================
    // USER SERVICE STATISTICS
    // =====================================================
    
    /**
     * Số lượng OTP của user
     */
    private Long otpCount;
    
    // =====================================================
    // PORTAL SERVICE STATISTICS  
    // =====================================================
    
    /**
     * Có liên kết với employee hay không
     */
    private Boolean hasEmployee;
    
    /**
     * Tên employee nếu có
     */
    private String employeeName;
    
    /**
     * Có liên kết với partner hay không
     */
    private Boolean hasPartner;
    
    /**
     * Tên partner nếu có
     */
    private String partnerName;
    
    /**
     * Số lượng thiết bị đã đăng ký
     */
    private Long deviceCount;
    
    /**
     * Số lượng salary advance limit
     */
    private Long salaryAdvanceLimitCount;
    
    // =====================================================
    // SALARY ADVANCE SERVICE STATISTICS
    // =====================================================
    
    /**
     * Số lượng transaction
     */
    private Long transactionCount;
    
    /**
     * Số lượng salary advance request
     */
    private Long salaryAdvanceRequestCount;
    
    /**
     * Tổng số bản ghi trong Salary Advance service
     */
    private Long totalSalaryAdvanceRecords;
    
    // =====================================================
    // SUMMARY STATISTICS
    // =====================================================
    
    /**
     * Tổng số bản ghi liên quan (tất cả services)
     */
    private Long totalRelatedRecords;
    
    /**
     * Mức độ rủi ro khi xóa user
     * LOW: Ít hoặc không có dữ liệu liên quan
     * MEDIUM: Có một số dữ liệu liên quan
     * HIGH: Có nhiều dữ liệu quan trọng (employee, partner, transactions)
     * CRITICAL: Có dữ liệu business critical
     */
    private RiskLevel riskLevel;
    
    /**
     * Có thể xóa an toàn với force=false hay không
     */
    private Boolean canSafeDelete;
    
    /**
     * Thông báo tóm tắt cho UI
     */
    private String summary;
    
    public enum RiskLevel {
        LOW,        // 0-5 records, không có employee/partner
        MEDIUM,     // 6-20 records, có thể có employee nhưng không có partner
        HIGH,       // 21-50 records, có employee hoặc partner
        CRITICAL    // >50 records hoặc có cả employee và partner
    }
    
    /**
     * Tính toán risk level dựa trên dữ liệu
     */
    public void calculateRiskLevel() {
        if (hasEmployee && hasPartner) {
            this.riskLevel = RiskLevel.CRITICAL;
            this.canSafeDelete = false;
            this.summary = "CRITICAL: User có cả employee và partner";
        } else if (totalRelatedRecords > 50) {
            this.riskLevel = RiskLevel.CRITICAL;
            this.canSafeDelete = false;
            this.summary = String.format("CRITICAL: %d bản ghi liên quan", totalRelatedRecords);
        } else if (hasEmployee || hasPartner || totalRelatedRecords > 20) {
            this.riskLevel = RiskLevel.HIGH;
            this.canSafeDelete = false;
            if (hasEmployee) {
                this.summary = String.format("HIGH: Liên kết với nhân viên %s", employeeName);
            } else if (hasPartner) {
                this.summary = String.format("HIGH: Liên kết với đối tác %s", partnerName);
            } else {
                this.summary = String.format("HIGH: %d bản ghi liên quan", totalRelatedRecords);
            }
        } else if (totalRelatedRecords > 5) {
            this.riskLevel = RiskLevel.MEDIUM;
            this.canSafeDelete = true;
            this.summary = String.format("MEDIUM: %d bản ghi liên quan", totalRelatedRecords);
        } else {
            this.riskLevel = RiskLevel.LOW;
            this.canSafeDelete = true;
            this.summary = totalRelatedRecords > 0 ? 
                String.format("LOW: %d bản ghi liên quan", totalRelatedRecords) : 
                "LOW: Không có dữ liệu liên quan";
        }
    }
    
    /**
     * Tính tổng số records
     */
    public void calculateTotals() {
        this.totalSalaryAdvanceRecords = (transactionCount != null ? transactionCount : 0) + 
                                       (salaryAdvanceRequestCount != null ? salaryAdvanceRequestCount : 0);
        
        this.totalRelatedRecords = (otpCount != null ? otpCount : 0) +
                                 (hasEmployee ? 1 : 0) +
                                 (hasPartner ? 1 : 0) +
                                 (deviceCount != null ? deviceCount : 0) +
                                 (salaryAdvanceLimitCount != null ? salaryAdvanceLimitCount : 0) +
                                 this.totalSalaryAdvanceRecords;
    }
}
