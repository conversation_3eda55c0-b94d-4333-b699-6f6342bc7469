spring.application.name=user
server.servlet.context-path=/user

logging.config=classpath:logback-spring.xml

server.port=8001

spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://identity.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/certs

spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.generate-ddl=true
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.show_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true
spring.jpa.properties.hibernate.globally_quoted_identifiers=false
spring.jpa.open-in-view=false

# Debug SQL parameter binding
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.pronexus.user=DEBUG

# Hibernate naming strategy - S? d?ng ?? t? ??ng chuy?n camelCase -> snake_case
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
spring.jpa.hibernate.naming.implicit-strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl

spring.jpa.properties.hibernate.default_schema=user_schema
spring.datasource.url=jdbc:postgresql://*************:5433/salary_advance_db
spring.datasource.username=postgres
spring.datasource.password=Thanhnx@123$
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=200
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000

keycloak.realm=pronexus_dev
keycloak.auth-server-url=https://identity.pronexus.vn

keycloak.master-username=admin
keycloak.master-password=admin
keycloak.master-client-id=admin-cli
keycloak.client-id=pronexus_dev
keycloak.client-secret=m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2

keycloak.admin.username=system-admin
keycloak.admin.password=123456

service.portal.url=https://api.pronexus.vn/portal
#service.portal.url=http://localhost:8000/portal

# Salary Advance Service Configuration
feign.portal.url=${service.portal.url}
feign.salary-advance.url=https://api.pronexus.vn/salary-advance
#feign.salary-advance.url=http://localhost:8001/salary-advance


otp.expiry.minutes=5
otp.max.attempts=3
otp.max.daily=10
otp.length=6



zns.api.url=https://business.openapi.zalo.me/message/template
zns.api.key=your-zns-api-key
zns.app.id=3676483894335813334
zns.secret.key=SG5T7VSWI9q8L34CGE2t
zns.otp.template.id=435884


spring.main.allow-bean-definition-overriding=true