# Salary Advance Service API Specification

## Overview

User service cần gọi trực tiếp đến Salary Advance service để xử lý dữ liệu liên quan đến employee khi xóa user.

## Required APIs

### 1. Get Employee Data Count

**Endpoint:** `GET /api/v1/employee/{employeeId}/data-count`

**Description:** Kiểm tra số lượng dữ liệu của employee trong tất cả các bảng liên quan

**Headers:**
```
Authorization: Bearer {token}
```

**Path Parameters:**
- `employeeId` (string): ID của employee (cũng là user ID từ Keycloak)

**Response:**
```json
{
  "success": true,
  "data": {
    "transaction": 5,
    "transaction_aud": 12,
    "salary_advance_request": 3,
    "salary_advance_request_aud": 8
  },
  "message": "Success"
}
```

**Response Fields:**
- `transaction`: <PERSON><PERSON> lượng bản ghi trong bảng `salary_advance.transaction`
- `transaction_aud`: <PERSON><PERSON> lượng bản ghi trong bảng `salary_advance.transaction_aud`
- `salary_advance_request`: Số lượng bản ghi trong bảng `salary_advance.salary_advance_request`
- `salary_advance_request_aud`: Số lượng bản ghi trong bảng `salary_advance.salary_advance_request_aud`

### 2. Delete All Employee Data

**Endpoint:** `DELETE /api/v1/employee/{employeeId}/all-data`

**Description:** Xóa tất cả dữ liệu của employee trong Salary Advance service

**Headers:**
```
Authorization: Bearer {token}
```

**Path Parameters:**
- `employeeId` (string): ID của employee (cũng là user ID từ Keycloak)

**Response:**
```json
{
  "success": true,
  "data": true,
  "message": "Successfully deleted all salary advance data for employee"
}
```

**Business Logic:**
1. Xóa tất cả records trong `salary_advance.transaction` WHERE `employee_id = {employeeId}`
2. Xóa tất cả records trong `salary_advance.transaction_aud` WHERE `employee_id = {employeeId}`
3. Xóa tất cả records trong `salary_advance.salary_advance_request` WHERE `employee_id = {employeeId}`
4. Xóa tất cả records trong `salary_advance.salary_advance_request_aud` WHERE `employee_id = {employeeId}`

**Error Handling:**
- Return `false` nếu có lỗi xảy ra
- Log chi tiết lỗi để debug
- Không throw exception, chỉ return false

## Implementation Notes

### Database Queries

```sql
-- Count queries for data-count endpoint
SELECT COUNT(*) FROM salary_advance.transaction WHERE employee_id = ?;
SELECT COUNT(*) FROM salary_advance.transaction_aud WHERE employee_id = ?;
SELECT COUNT(*) FROM salary_advance.salary_advance_request WHERE employee_id = ?;
SELECT COUNT(*) FROM salary_advance.salary_advance_request_aud WHERE employee_id = ?;

-- Delete queries for all-data endpoint
DELETE FROM salary_advance.transaction WHERE employee_id = ?;
DELETE FROM salary_advance.transaction_aud WHERE employee_id = ?;
DELETE FROM salary_advance.salary_advance_request WHERE employee_id = ?;
DELETE FROM salary_advance.salary_advance_request_aud WHERE employee_id = ?;
```

### Security

- Cần validate JWT token
- Chỉ cho phép service-to-service calls
- Log tất cả operations để audit

### Performance

- Sử dụng transaction để đảm bảo data consistency
- Có thể cần batch delete nếu data lớn
- Consider soft delete thay vì hard delete nếu cần retain data

## Integration with User Service

User service sẽ gọi các APIs này trong quá trình xóa user:

1. **checkUserDeletionWarnings()**: Gọi `data-count` để hiển thị cảnh báo
2. **deleteUser()**: Gọi `all-data` để xóa dữ liệu thực tế

## Testing

### Test Cases

1. **Employee có dữ liệu**: Verify count và delete hoạt động đúng
2. **Employee không có dữ liệu**: Verify trả về 0 và true
3. **Employee không tồn tại**: Verify trả về empty map và true
4. **Database error**: Verify error handling

### Sample Test Data

```sql
-- Insert test data
INSERT INTO salary_advance.transaction (employee_id, amount, status) VALUES ('test-user-id', 1000000, 'COMPLETED');
INSERT INTO salary_advance.salary_advance_request (employee_id, requested_amount, status) VALUES ('test-user-id', 500000, 'APPROVED');
```
