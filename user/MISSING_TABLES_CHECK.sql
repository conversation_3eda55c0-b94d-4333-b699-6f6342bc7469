-- =====================================================
-- KIỂM TRA CÁC BẢNG THIẾU TRONG HÀM XÓA USER
-- =====================================================
-- User ID để test: '8c1515b6-0217-4156-9678-13691353fa28'

-- =====================================================
-- 1. SALARY ADVANCE SERVICE - Các bảng thiếu
-- =====================================================

-- <PERSON><PERSON>m tra transaction
SELECT 'salary_advance.transaction' as table_name, COUNT(*) as record_count 
FROM salary_advance.transaction 
WHERE employee_id = '8c1515b6-0217-4156-9678-13691353fa28';

-- Kiểm tra transaction_aud  
SELECT 'salary_advance.transaction_aud' as table_name, COUNT(*) as record_count
FROM salary_advance.transaction_aud 
WHERE employee_id = '8c1515b6-0217-4156-9678-13691353fa28';

-- Kiểm tra salary_advance_request
SELECT 'salary_advance.salary_advance_request' as table_name, COUNT(*) as record_count
FROM salary_advance.salary_advance_request 
WHERE employee_id = '8c1515b6-0217-4156-9678-13691353fa28';

-- Kiểm tra salary_advance_request_aud
SELECT 'salary_advance.salary_advance_request_aud' as table_name, COUNT(*) as record_count
FROM salary_advance.salary_advance_request_aud 
WHERE employee_id = '8c1515b6-0217-4156-9678-13691353fa28';

-- =====================================================
-- 2. PORTAL SERVICE - Các bảng thiếu  
-- =====================================================

-- Kiểm tra salary_advance_limit
SELECT 'portal.salary_advance_limit' as table_name, COUNT(*) as record_count
FROM portal.salary_advance_limit 
WHERE employee_id = '8c1515b6-0217-4156-9678-13691353fa28';

-- Kiểm tra salary_advance_limit_aud
SELECT 'portal.salary_advance_limit_aud' as table_name, COUNT(*) as record_count
FROM portal.salary_advance_limit_aud 
WHERE employee_id = '8c1515b6-0217-4156-9678-13691353fa28';

-- =====================================================
-- 3. PORTAL SERVICE - Các bảng đã xử lý (để so sánh)
-- =====================================================

-- Kiểm tra user_device (đã xử lý)
SELECT 'portal.user_device' as table_name, COUNT(*) as record_count
FROM portal.user_device 
WHERE user_id = '8c1515b6-0217-4156-9678-13691353fa28';

-- Kiểm tra user_partner (đã xử lý)  
SELECT 'portal.user_partner' as table_name, COUNT(*) as record_count
FROM portal.user_partner 
WHERE user_id = '8c1515b6-0217-4156-9678-13691353fa28';

-- =====================================================
-- 4. TỔNG HỢP TẤT CẢ CÁC BẢNG LIÊN QUAN
-- =====================================================

SELECT 
    'SUMMARY' as section,
    'Các bảng CHƯA được xử lý trong hàm deleteUser:' as description
UNION ALL
SELECT 
    '- salary_advance.transaction',
    'Giao dịch ứng lương'
UNION ALL
SELECT 
    '- salary_advance.transaction_aud', 
    'Audit log giao dịch'
UNION ALL
SELECT 
    '- salary_advance.salary_advance_request',
    'Yêu cầu ứng lương'
UNION ALL
SELECT 
    '- salary_advance.salary_advance_request_aud',
    'Audit log yêu cầu ứng lương'
UNION ALL
SELECT 
    '- portal.salary_advance_limit',
    'Hạn mức ứng lương'
UNION ALL
SELECT 
    '- portal.salary_advance_limit_aud',
    'Audit log hạn mức ứng lương';

-- =====================================================
-- 5. SCRIPT XÓA DỮ LIỆU (CHỈ CHẠY KHI CẦN THIẾT)
-- =====================================================

/*
-- CẢNH BÁO: CHỈ CHẠY KHI THỰC SỰ CẦN XÓA USER
-- Thay thế USER_ID_HERE bằng ID thực tế

-- Xóa dữ liệu Salary Advance Service
DELETE FROM salary_advance.transaction WHERE employee_id = 'USER_ID_HERE';
DELETE FROM salary_advance.transaction_aud WHERE employee_id = 'USER_ID_HERE';
DELETE FROM salary_advance.salary_advance_request WHERE employee_id = 'USER_ID_HERE';
DELETE FROM salary_advance.salary_advance_request_aud WHERE employee_id = 'USER_ID_HERE';

-- Xóa dữ liệu Portal Service
DELETE FROM portal.salary_advance_limit WHERE employee_id = 'USER_ID_HERE';
DELETE FROM portal.salary_advance_limit_aud WHERE employee_id = 'USER_ID_HERE';
*/
