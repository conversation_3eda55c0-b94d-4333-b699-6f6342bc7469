# TODO: Implement Delete Employee API

## 🎯 **BUSINESS REQUIREMENT**

**Super admin với force=true → XÓA THẲNG employee!**

## 📋 **TASKS TO IMPLEMENT**

### **1. Portal Service - Add deleteEmployee API**

#### **EmployeeController.java:**
```java
@DeleteMapping("/{id}/force")
public ResponseEntity<Response<CommandResponse<Void>>> forceDeleteEmployee(
        @PathVariable Long id) {
    CommandResponse<Void> body = employeeService.forceDelete(id);
    return body.isSuccess() ? Response.success(body) : Response.failure(body);
}
```

#### **EmployeeService.java:**
```java
/**
 * Force delete employee - xóa hoàn toàn employee và tất cả dữ liệu liên quan
 * CHỈ dành cho super admin
 */
CommandResponse<Void> forceDelete(Long employeeId);
```

#### **EmployeeServiceImpl.java:**
```java
@Override
@Transactional
public CommandResponse<Void> forceDelete(Long employeeId) {
    try {
        EmployeeEntity employee = employeeRepository.findById(employeeId)
            .orElseThrow(() -> new NotFoundException("Employee không tồn tại"));
        
        // Xóa tất cả dữ liệu liên quan
        // 1. Xóa salary records
        // 2. Xóa attendance records  
        // 3. Xóa timesheet records
        // 4. Xóa salary advance limits
        // 5. Xóa employee
        
        employeeRepository.delete(employee);
        
        return CommandResponse.success(null, "Đã xóa hoàn toàn employee");
    } catch (Exception e) {
        return CommandResponse.failure(null, "Lỗi khi xóa employee: " + e.getMessage());
    }
}
```

### **2. Portal Client - Add deleteEmployee method**

#### **PortalClient.java:**
```java
/**
 * Force delete employee - chỉ dành cho super admin
 */
boolean deleteEmployee(Long employeeId);
```

#### **PortalClientImpl.java:**
```java
@Override
public boolean deleteEmployee(Long employeeId) {
    try {
        ResponseEntity<Response<CommandResponse<Void>>> response = 
            portalFeignClient.forceDeleteEmployee(getAuthHeader(), employeeId);
        
        return response.getBody() != null && 
               response.getBody().getData() != null && 
               response.getBody().getData().isSuccess();
    } catch (Exception e) {
        log.error("Error deleting employee {}: {}", employeeId, e.getMessage(), e);
        return false;
    }
}
```

### **3. Portal Feign Client - Add API call**

#### **PortalFeignClient.java:**
```java
@DeleteMapping("/api/v1/employee/{id}/force")
ResponseEntity<Response<CommandResponse<Void>>> forceDeleteEmployee(
    @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
    @PathVariable("id") Long id);
```

### **4. User Service - Enable force delete**

#### **UserServiceImpl.java:**
```java
// Uncomment và enable force delete
if (force) {
    // Option 2: DELETE employee hoàn toàn (Super admin force mode)
    boolean deleted = portalClient.deleteEmployee(employee.getId());
    if (deleted) {
        deletionLog.add("🔥 FORCE DELETE: Đã xóa hoàn toàn nhân viên: " + employee.getName());
        log.info("FORCE DELETE: Đã xóa hoàn toàn employee {}", employee.getId());
    } else {
        deletionLog.add("❌ FORCE DELETE: Lỗi khi xóa nhân viên: " + employee.getName());
        log.error("FORCE DELETE: Lỗi khi xóa employee {}", employee.getId());
    }
}
```

## 🔒 **SECURITY CONSIDERATIONS**

### **1. Role-based Access:**
```java
@PreAuthorize("hasRole('SUPER_ADMIN')")
@DeleteMapping("/{id}/force")
public ResponseEntity<Response<CommandResponse<Void>>> forceDeleteEmployee(@PathVariable Long id) {
    // Only super admin can access
}
```

### **2. Audit Logging:**
```java
@Override
public CommandResponse<Void> forceDelete(Long employeeId) {
    // Log critical action
    auditService.logCriticalAction(
        "FORCE_DELETE_EMPLOYEE", 
        employeeId, 
        getCurrentUser(),
        "Super admin force deleted employee"
    );
    
    // Proceed with deletion
}
```

## ⚠️ **DATA SAFETY**

### **1. Cascade Deletion Strategy:**
```java
// Xóa theo thứ tự để tránh foreign key constraint
1. Salary advance transactions (salary-advance service)
2. Salary advance requests (salary-advance service)  
3. Salary advance limits (portal)
4. Attendance records (portal)
5. Timesheet records (portal)
6. Salary records (portal)
7. Employee entity (portal)
```

### **2. Backup Before Delete:**
```java
@Override
public CommandResponse<Void> forceDelete(Long employeeId) {
    // Backup employee data before deletion
    EmployeeBackup backup = createEmployeeBackup(employeeId);
    backupService.save(backup);
    
    // Proceed with deletion
}
```

## 🧪 **TESTING PLAN**

### **1. Unit Tests:**
- Test force delete với super admin role
- Test access denied với normal user
- Test cascade deletion
- Test error handling

### **2. Integration Tests:**
- Test full flow: User service → Portal service
- Test transaction rollback on failure
- Test audit logging

### **3. Manual Testing:**
- Test với real data
- Verify all related data deleted
- Check audit logs
- Test rollback scenarios

## 📅 **IMPLEMENTATION TIMELINE**

### **Phase 1: Portal Service (1-2 days)**
- [ ] Add forceDeleteEmployee API
- [ ] Implement cascade deletion logic
- [ ] Add security checks
- [ ] Unit tests

### **Phase 2: Portal Client (0.5 day)**
- [ ] Add deleteEmployee method
- [ ] Add Feign client call
- [ ] Error handling

### **Phase 3: User Service (0.5 day)**
- [ ] Enable force delete logic
- [ ] Update logging
- [ ] Integration testing

### **Phase 4: Testing & Documentation (1 day)**
- [ ] Integration tests
- [ ] Manual testing
- [ ] Update documentation
- [ ] Security review

## 🚀 **DEPLOYMENT PLAN**

### **1. Staging Deployment:**
- Deploy Portal service first
- Deploy User service second
- Test end-to-end flow

### **2. Production Deployment:**
- Deploy during maintenance window
- Monitor logs carefully
- Have rollback plan ready

## 📚 **DOCUMENTATION UPDATES**

- [ ] Update API documentation
- [ ] Update security guidelines
- [ ] Update admin user guide
- [ ] Update troubleshooting guide

## ✅ **ACCEPTANCE CRITERIA**

- [ ] Super admin can force delete employee
- [ ] Normal users cannot access force delete
- [ ] All related data is deleted
- [ ] Audit logs are created
- [ ] Error handling works correctly
- [ ] Performance is acceptable
- [ ] Security is maintained
