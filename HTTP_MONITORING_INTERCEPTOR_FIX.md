# HttpMonitoringInterceptor Fix Summary

## 🐛 **Vấn đề gặp phải:**

### Error Message:
```
Cannot deserialize value of type `java.util.LinkedHashMap<java.lang.String,java.lang.Object>` 
from Array value (token `JsonToken.START_ARRAY`)
```

### Root Cause:
- `HttpMonitoringInterceptor.sanitizeByteArrayFields()` cố gắng convert response body thành `Map<String, Object>`
- Khi API trả về List (như `GET /api/v1/tasks` → `List<TaskResponse>`), <PERSON> không thể convert Array thành Map
- Lỗi xảy ra ở dòng 137: `Map<String, Object> map = mapper.convertValue(body, new TypeReference<>() {});`

## 🔧 **Giải pháp đã áp dụng:**

### 1. **Thay đổi approach:**
```java
// Trước (chỉ handle Map)
Map<String, Object> map = mapper.convertValue(body, new TypeReference<>() {});

// Sau (handle cả Object và Array)
String jsonString = mapper.writeValueAsString(body);
Object parsedObject = mapper.readValue(jsonString, Object.class);
return sanitizeRecursively(parsedObject);
```

### 2. **Thêm recursive sanitization:**
```java
@SuppressWarnings("unchecked")
private Object sanitizeRecursively(Object obj) {
    if (obj instanceof Map) {
        // Handle Map objects
        Map<String, Object> map = (Map<String, Object>) obj;
        map.replaceAll((key, value) -> {
            if (value instanceof byte[]) {
                return "binary data omitted";
            } else if (value instanceof Map || value instanceof List) {
                return sanitizeRecursively(value);
            }
            return value;
        });
        return map;
    } else if (obj instanceof List) {
        // Handle List objects
        List<Object> list = (List<Object>) obj;
        return list.stream()
                .map(this::sanitizeRecursively)
                .collect(Collectors.toList());
    } else if (obj instanceof byte[]) {
        // Handle byte arrays
        return "binary data omitted";
    }
    return obj;
}
```

### 3. **Imports thêm:**
```java
import java.util.List;
import java.util.stream.Collectors;
```

## 🎯 **Lợi ích của fix:**

### 1. **Universal Support:**
- ✅ Handle Map objects (existing functionality)
- ✅ Handle List/Array objects (new functionality)
- ✅ Handle nested structures (recursive)
- ✅ Handle primitive types và byte arrays

### 2. **Backward Compatibility:**
- ✅ Không breaking changes cho existing APIs
- ✅ Vẫn sanitize byte arrays như trước
- ✅ Error handling vẫn fallback về original body

### 3. **Robust Error Handling:**
```java
try {
    // New sanitization logic
    return sanitizeRecursively(parsedObject);
} catch (Exception e) {
    log.error("Error sanitizing byte array fields: {}", e.getMessage(), e);
    return body; // Fallback to original
}
```

## 📊 **Test Cases Covered:**

### 1. **Single Object Response:**
```json
{
  "statusCode": null,
  "httpStatusCode": 200,
  "data": {
    "id": "uuid",
    "name": "Task Name"
  }
}
```
✅ **Result**: Works as before

### 2. **Array Response:**
```json
{
  "statusCode": null,
  "httpStatusCode": 200,
  "data": [
    {
      "id": "uuid1",
      "name": "Task 1"
    },
    {
      "id": "uuid2", 
      "name": "Task 2"
    }
  ]
}
```
✅ **Result**: Now works correctly (was failing before)

### 3. **Nested Structures:**
```json
{
  "data": {
    "tasks": [
      {
        "id": "uuid",
        "metadata": {
          "binaryData": "binary data omitted"
        }
      }
    ]
  }
}
```
✅ **Result**: Recursively sanitizes all levels

### 4. **Byte Array Sanitization:**
```java
// Input
{
  "data": {
    "fileContent": [byte array],
    "tasks": [
      {
        "attachment": [byte array]
      }
    ]
  }
}

// Output
{
  "data": {
    "fileContent": "binary data omitted",
    "tasks": [
      {
        "attachment": "binary data omitted"
      }
    ]
  }
}
```
✅ **Result**: All byte arrays sanitized recursively

## 🚀 **Impact:**

### **Before Fix:**
- ❌ `GET /api/v1/tasks` → Error 500
- ❌ Any API returning List/Array → Error 500
- ✅ APIs returning single objects → OK

### **After Fix:**
- ✅ `GET /api/v1/tasks` → OK
- ✅ All APIs returning List/Array → OK  
- ✅ APIs returning single objects → OK
- ✅ Nested structures → OK
- ✅ Byte array sanitization → OK

## 🔍 **Monitoring:**

### **Log Output Example:**
```
INFO  c.s.c.c.HttpMonitoringInterceptor - beforeBodyWrite Response body: {
  "statusCode": null,
  "httpStatusCode": 200,
  "data": [
    {
      "id": "uuid1",
      "name": "ZNS_TOKEN_REFRESH",
      "jobType": "ZNS_TOKEN_REFRESH",
      "isActive": true
    }
  ]
}
```

### **Error Handling:**
- Nếu sanitization fail → fallback về original body
- Log error nhưng không crash application
- Graceful degradation

## ✅ **Verification:**

### **Test Commands:**
```bash
# Test single object
curl -H "Authorization: Bearer TOKEN" https://api.pronexus.vn/job/api/v1/tasks/uuid

# Test array response (was failing before)
curl -H "Authorization: Bearer TOKEN" https://api.pronexus.vn/job/api/v1/tasks

# Both should work without HttpMonitoringInterceptor errors
```

Fix này đảm bảo `HttpMonitoringInterceptor` hoạt động với mọi loại response structure! 🎯
