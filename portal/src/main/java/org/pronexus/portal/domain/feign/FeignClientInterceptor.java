package org.pronexus.portal.domain.feign;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class FeignClientInterceptor implements RequestInterceptor {

    private static final String BEARER_TOKEN = "Bearer";
    @Override
    public void apply(RequestTemplate template) {
        var auth = template.headers().get(HttpHeaders.AUTHORIZATION);
        if( !CollectionUtils.isEmpty(auth)) return;

        var authentication = SecurityContextHolder.getContext().getAuthentication();
        if(authentication == null) return;
        Jwt jwt = ((JwtAuthenticationToken) authentication).getToken();
        var token = jwt.getTokenValue();
        template.header(HttpHeaders.AUTHORIZATION, String.format("%s %s", BEARER_TOKEN, token));
    }
}
