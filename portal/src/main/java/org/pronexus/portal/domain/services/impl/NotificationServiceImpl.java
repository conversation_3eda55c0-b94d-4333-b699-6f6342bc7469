package org.pronexus.portal.domain.services.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.firebase.messaging.BatchResponse;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.google.firebase.messaging.MulticastMessage;
import com.salaryadvance.commonlibrary.exception.BadRequestException;
import com.salaryadvance.commonlibrary.exception.base.ResourceNotFoundException;
import com.salaryadvance.commonlibrary.rest.Response;
import com.salaryadvance.commonlibrary.utils.HtmlUtils;
import com.salaryadvance.commonlibrary.utils.JsonUtils;
import com.salaryadvance.commonlibrary.utils.TokenUtils;
import lombok.extern.log4j.Log4j2;
import org.pronexus.portal.app.dtos.SendNotifySalaryAdvanceDto;
import org.pronexus.portal.app.dtos.notification.CreateUpdateNotificationDto;
import org.pronexus.portal.app.dtos.notification.NotificationCriteriaDto;
import org.pronexus.portal.app.dtos.notification.SendNotifyDto;
import org.pronexus.portal.app.response.NotificationDetailRes;
import org.pronexus.portal.app.response.NotificationHistoryRes;
import org.pronexus.portal.app.response.UserDeviceRes;
import org.pronexus.portal.domain.data.ExtraNotifyData;
import org.pronexus.portal.domain.entities.Notification;
import org.pronexus.portal.domain.entities.NotificationRecipient;
import org.pronexus.portal.domain.entities.type.*;
import org.pronexus.portal.domain.feign.clients.UserFeignClient;
import org.pronexus.portal.domain.feign.replies.UserDeviceReply;
import org.pronexus.portal.domain.mappers.NotificationMapper;
import org.pronexus.portal.domain.repositories.specification.NotificationSpecification;
import org.springframework.data.jpa.domain.Specification;
import org.pronexus.portal.domain.repositories.UserDeviceRepository;
import org.pronexus.portal.domain.services.core.NotificationService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.pronexus.portal.domain.services.core.UserDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Log4j2
public class NotificationServiceImpl extends BaseService implements NotificationService {
    @Autowired
    private NotificationMapper modelMapper;

    @Autowired
    private UserFeignClient userClient;

    @Autowired
    private UserDeviceService userDeviceService;

    @Autowired
    private UserDeviceRepository userDeviceRepository;

    @Autowired
    private NotificationSpecification notificationSpecification;

    @Override
    public Boolean create(CreateUpdateNotificationDto dto) {
        String username = TokenUtils.getUsername();
        Notification notification = modelMapper.toNotification(dto);
        notification.setCreatedBy(username);
        notification.setUpdatedBy(username);
        notification.setStatus(NotificationStatus.ACTIVE);
        notificationRepository.save(notification);

        // Nếu push=true, gửi thông báo ngay lập tức
        if (Boolean.TRUE.equals(dto.getPush())) {
            // Nếu không truyền userIds thì gửi cho tất cả người dùng có thiết bị
            if (dto.getUserIds() == null || dto.getUserIds().isEmpty()) {
                log.info("No userIds provided, sending notification to all active users");
                List<String> allUserIds = userDeviceRepository.findDistinctUserIdsByStatus(UserDeviceStatus.ACTIVE);
                dto.setUserIds(allUserIds);
                log.info("Found {} active users to send notification", allUserIds.size());
            }
            // Tạo một traceId mới cho việc gửi thông báo
            String traceId = UUID.randomUUID().toString();
            log.info("Created new traceId: {} for push notification", traceId);

            List<NotificationRecipient> notificationRecipients = new ArrayList<>();
            for (String userId : dto.getUserIds()) {
                // Kiểm tra xem đã có bản ghi nào cho người dùng này và thông báo này chưa
                NotificationRecipient existingRecipient = notificationRecipientRepository
                        .findByNotificationIdAndRecipientId(notification.getId(), userId);

                if (existingRecipient != null) {
                    // Nếu đã có bản ghi, cập nhật trạng thái và thời gian gửi
                    existingRecipient.setSentAt(System.currentTimeMillis());
                    existingRecipient.setStatus(NotificationRecipientStatus.PENDING);
                    existingRecipient.setPlatform(dto.getPlatform());
                    existingRecipient.setExtra(notification.getExtra());
                    notificationRecipients.add(existingRecipient);
                    log.info("{} Updated existing notification recipient for userId: {} and notificationId: {}",
                            traceId, userId, notification.getId());
                } else {
                    // Nếu chưa có bản ghi, tạo mới
                    NotificationRecipient recipient = new NotificationRecipient();
                    recipient.setNotificationId(notification.getId());
                    recipient.setRecipientId(userId);
                    recipient.setSentAt(System.currentTimeMillis());
                    recipient.setStatus(NotificationRecipientStatus.PENDING); // Đặt trạng thái ban đầu là PENDING
                    recipient.setPlatform(dto.getPlatform());
                    recipient.setExtra(notification.getExtra());
                    recipient.setIsRead(false);
                    notificationRecipients.add(recipient);
                    log.info("{} Created new notification recipient for userId: {} and notificationId: {}", traceId,
                            userId, notification.getId());
                }
            }
            notificationRecipientRepository.saveAll(notificationRecipients);

            // Gửi thông báo
            SendNotifyDto sendNotifyDto = new SendNotifyDto();
            sendNotifyDto.setNotificationId(notification.getId());
            sendNotifyDto.setUserIds(dto.getUserIds());
            sendNotifyDto.setPlatform(dto.getPlatform());
            sendNotifyBy(sendNotifyDto);
        }

        return true;
    }

    @Override
    public Boolean update(Integer id, CreateUpdateNotificationDto dto) {
        String username = TokenUtils.getUsername();
        Notification notification = notificationRepository.findNotificationById(id);
        if (notification == null) {
            throw new ResourceNotFoundException("Không tìm thấy thông tin bản ghi id: " + id);
        }
        notification = modelMapper.toNotification(dto);
        notification.setId(id);
        notification.setCreatedBy(username);
        notification.setUpdatedBy(username);
        notificationRepository.save(notification);
        return true;
    }

    @Override
    @Transactional
    public Boolean delete(Integer id) {
        Notification notification = notificationRepository.findNotificationById(id);
        if (notification == null) {
            throw new ResourceNotFoundException("Không tìm thấy thông tin bản ghi id: " + id);
        }

        // Xóa các bản ghi NotificationRecipient liên quan trước
        notificationRecipientRepository.deleteByNotificationId(id);

        // Xóa thông báo trực tiếp từ cơ sở dữ liệu
        notificationRepository.deleteById(id);

        return true;
    }

    @Override
    public NotificationDetailRes detail(Integer id) {
        Notification notification = notificationRepository.findNotificationById(id);
        if (notification == null) {
            throw new ResourceNotFoundException("Không tìm thấy thông tin bản ghi id: " + id);
        }
        return modelMapper.toNotificationDetailRes(notification);
    }

    @Override
    public List<NotificationDetailRes> getNotificationDetailRes() {
        return null;
    }

    @Override
    public Boolean sendNotifyBy(SendNotifyDto dto) {
        Notification notification = notificationRepository.findNotificationByIdAndStatus(dto.getNotificationId(),
                NotificationStatus.ACTIVE);
        if (notification == null) {
            throw new ResourceNotFoundException("Không tìm thấy thông tin bản ghi id: " + dto.getNotificationId());
        }

        List<String> userIds;
        List<String> tokens;
        List<UserDeviceRes> userDevices;

        // Xử lý userIds và lấy tokens
        if (dto.getUserIds() != null && !dto.getUserIds().isEmpty()) {
            // Sử dụng userIds từ dto
            userIds = dto.getUserIds();
            log.info("sendNotifyBy using provided userIds: {}", userIds);
        } else {
            // Nếu không có userIds, lấy tất cả người dùng có thiết bị
            // Sử dụng repository trực tiếp để lấy danh sách userIds duy nhất
            List<String> distinctUserIds = userDeviceRepository.findDistinctUserIdsByStatus(UserDeviceStatus.ACTIVE);
            userIds = distinctUserIds;
            log.info("sendNotifyBy using all active userIds: {}", userIds.size());
        }

        // Sử dụng phương thức mới để lấy tokens
        tokens = userDeviceService.getDeviceTokensByUserIds(userIds);
        log.info("sendNotifyBy got {} tokens", tokens.size());

        // Lấy thông tin thiết bị cho các userIds cụ thể
        List<UserDeviceRes> userDevicesForIds = new ArrayList<>();
        for (String userId : userIds) {
            List<UserDeviceRes> devices = userDeviceService.getUserDevice(userId);
            userDevicesForIds.addAll(devices);
        }
        userDevices = userDevicesForIds;

        // Lọc theo platform nếu có
        if (dto.getPlatform() != null && dto.getPlatform() != NotifyPlatformType.ALL) {
            userDevices = userDevices.stream().filter(v -> v.getDeviceType().name().equals(dto.getPlatform().name()))
                    .toList();

            // Cần lọc lại tokens nếu có platform
            tokens = userDevices.stream()
                    .map(UserDeviceRes::getDeviceToken)
                    .filter(token -> token != null && !token.isEmpty())
                    .distinct()
                    .toList();
        }

        // Tạo mới hoặc cập nhật các bản ghi NotificationRecipient
        List<NotificationRecipient> notificationRecipients = new ArrayList<>();
        for (UserDeviceRes userDevice : userDevices) {
            // Kiểm tra xem đã có bản ghi nào cho người dùng này và thông báo này chưa
            NotificationRecipient existingRecipient = notificationRecipientRepository
                    .findByNotificationIdAndRecipientId(notification.getId(), userDevice.getUserId());

            if (existingRecipient != null) {
                // Nếu đã có bản ghi, cập nhật trạng thái và thời gian gửi
                existingRecipient.setSentAt(System.currentTimeMillis());
                existingRecipient.setStatus(NotificationRecipientStatus.PENDING);
                existingRecipient.setPlatform(userDevice.getDeviceType().name().equals("IOS") ? NotifyPlatformType.IOS
                        : NotifyPlatformType.ANDROID);
                existingRecipient.setExtra(notification.getExtra());
                existingRecipient.setDeviceToken(userDevice.getDeviceToken());
                notificationRecipients.add(existingRecipient);
                log.info("Updated existing notification recipient for userId: {} and notificationId: {}",
                        userDevice.getUserId(), notification.getId());
            } else {
                // Nếu chưa có bản ghi, tạo mới
                NotificationRecipient notifyRecipient = new NotificationRecipient();
                notifyRecipient.setNotificationId(notification.getId());
                notifyRecipient.setRecipientId(userDevice.getUserId());
                notifyRecipient.setSentAt(System.currentTimeMillis());
                notifyRecipient.setStatus(NotificationRecipientStatus.PENDING);
                notifyRecipient.setPlatform(userDevice.getDeviceType().name().equals("IOS") ? NotifyPlatformType.IOS
                        : NotifyPlatformType.ANDROID);
                notifyRecipient.setExtra(notification.getExtra());
                notifyRecipient.setIsRead(false);
                notifyRecipient.setDeviceToken(userDevice.getDeviceToken());
                notificationRecipients.add(notifyRecipient);
                log.info("Created new notification recipient for userId: {} and notificationId: {}",
                        userDevice.getUserId(), notification.getId());
            }
        }

        // Lưu các bản ghi NotificationRecipient
        if (!notificationRecipients.isEmpty()) {
            notificationRecipientRepository.saveAll(notificationRecipients);
        }

        // Gửi thông báo qua Firebase
        if (!tokens.isEmpty()) {
            // Convert HTML body to plain text before sending
            String plainTextBody = HtmlUtils.htmlToText(notification.getBody());

            com.google.firebase.messaging.Notification notificationFirebase = com.google.firebase.messaging.Notification
                    .builder()
                    .setTitle(notification.getTitle())
                    .setBody(plainTextBody)
                    .build();

            MulticastMessage.Builder messageBuilder = MulticastMessage.builder()
                    .addAllTokens(tokens)
                    .setNotification(notificationFirebase);

            // Thêm thông tin deepLink nếu có
            if (notification.getButtonName() != null && notification.getButtonAction() != null) {
                messageBuilder.putData("buttonName", notification.getButtonName());
                messageBuilder.putData("buttonAction", notification.getButtonAction());
            }

            MulticastMessage message = messageBuilder.build();
            try {
                BatchResponse batchResponse = FirebaseMessaging.getInstance().sendEachForMulticast(message);
                Map<String, Object> map = Map.of(
                        "successCount", batchResponse.getSuccessCount(),
                        "failureCount", batchResponse.getFailureCount());
                log.info("processNotification notificationFirebase batchResponse: {}",
                        JsonUtils.toJson(map));

                // Cập nhật trạng thái của các bản ghi NotificationRecipient thành SENT
                if (batchResponse.getSuccessCount() > 0) {
                    for (NotificationRecipient recipient : notificationRecipients) {
                        recipient.setStatus(NotificationRecipientStatus.SENT);
                    }
                    notificationRecipientRepository.saveAll(notificationRecipients);
                }
            } catch (FirebaseMessagingException e) {
                log.error("processNotification FirebaseMessagingException: {}", e);
            }
        } else {
            log.warn("No valid device tokens found for notification ID: {}", notification.getId());
        }

        return true;
    }

    @Override
    public Boolean sendNotifySalaryAdvance(SendNotifySalaryAdvanceDto dto) {
        log.info("sendNotifySalaryAdvance employeeId: {}, transId: {}", dto.getUserId(), dto.getTransId());

        // Tạo một thông báo mới thay vì tìm kiếm thông báo có sẵn
        Notification notification = new Notification();
        notification.setEvent(NotificationEventType.SALARY_ADVANCE_SUCCESS);
        notification.setTitle("Thông báo ứng lương thành công " + dto.getTransId());
        notification.setBody("Giao dịch ứng lương của bạn đã được xử lý thành công");
        notification.setPlatform(NotifyPlatformType.ALL);
        notification.setExtra(JsonUtils.toJson(new ExtraNotifyData(dto.getTransId())));
        notification.setStatus(NotificationStatus.ACTIVE);

        try {
            // Lưu thông báo mới vào cơ sở dữ liệu
            notification = notificationRepository.save(notification);

            // Kiểm tra xem thông báo đã được lưu thành công và có ID chưa
            if (notification.getId() == null) {
                log.error("Failed to create notification for salary advance: ID is null");
                return false;
            }

            log.info("Created new notification for salary advance: {}", notification.getId());

            // Tạo bản ghi NotificationRecipient
            NotificationRecipient notifyRecipient = new NotificationRecipient();
            notifyRecipient.setNotificationId(notification.getId());
            notifyRecipient.setRecipientId(dto.getUserId());
            notifyRecipient.setSentAt(System.currentTimeMillis());
            notifyRecipient.setStatus(NotificationRecipientStatus.PENDING);
            notifyRecipient.setPlatform(NotifyPlatformType.ALL);
            notifyRecipient.setExtra(JsonUtils.toJson(new ExtraNotifyData(dto.getTransId())));
            notifyRecipient.setIsRead(false);
            notificationRecipientRepository.save(notifyRecipient);
        } catch (Exception e) {
            log.error("Error creating notification for salary advance: {}", e.getMessage());
            return false;
        }

        // Lấy danh sách thiết bị của người dùng
        List<UserDeviceRes> userDeviceRes = userDeviceService.getUserDevice(dto.getUserId());
        log.info("sendNotifySalaryAdvance userDeviceRes: {}", userDeviceRes);

        if (userDeviceRes != null && !userDeviceRes.isEmpty()) {
            // Lấy danh sách tokens hợp lệ
            List<String> tokens = userDeviceRes.stream()
                    .map(UserDeviceRes::getDeviceToken)
                    .filter(token -> token != null && !token.isEmpty())
                    .toList();

            if (!tokens.isEmpty()) {
                // Tạo thông báo Firebase
                // Convert HTML body to plain text before sending
                String plainTextBody = HtmlUtils.htmlToText(notification.getBody());

                com.google.firebase.messaging.Notification notificationFirebase = com.google.firebase.messaging.Notification
                        .builder()
                        .setTitle(notification.getTitle())
                        .setBody(plainTextBody)
                        .build();

                // Gửi thông báo
                MulticastMessage.Builder messageBuilder = MulticastMessage.builder()
                        .addAllTokens(tokens)
                        .setNotification(notificationFirebase);

                MulticastMessage message = messageBuilder.build();
                try {
                    BatchResponse batchResponse = FirebaseMessaging.getInstance().sendEachForMulticast(message);
                    Map<String, Object> map = Map.of(
                            "successCount", batchResponse.getSuccessCount(),
                            "failureCount", batchResponse.getFailureCount());
                    log.info("sendNotifySalaryAdvance notificationFirebase batchResponse: {}",
                            JsonUtils.toJson(map));

                    // Cập nhật trạng thái của bản ghi NotificationRecipient thành SENT nếu gửi
                    // thành công
                    if (batchResponse.getSuccessCount() > 0) {
                        try {
                            // Tìm lại bản ghi NotificationRecipient để cập nhật trạng thái
                            NotificationRecipient recipient = notificationRecipientRepository
                                    .findByNotificationIdAndRecipientId(notification.getId(), dto.getUserId());
                            if (recipient != null) {
                                recipient.setStatus(NotificationRecipientStatus.SENT);
                                notificationRecipientRepository.save(recipient);
                                log.info(
                                        "Updated notification recipient status to SENT for userId: {} and notificationId: {}",
                                        dto.getUserId(), notification.getId());
                            }
                        } catch (Exception e) {
                            log.error("Error updating notification recipient status: {}", e.getMessage());
                            // Không return false ở đây vì thông báo đã được gửi thành công
                        }
                        return true;
                    }
                } catch (FirebaseMessagingException e) {
                    log.error("sendNotifySalaryAdvance FirebaseMessagingException: {}", e);
                }
            } else {
                log.warn("No valid device tokens found for user: {}", dto.getUserId());
            }
        } else {
            log.warn("No devices found for user: {}", dto.getUserId());
        }

        return false;
    }

    @Override
    public Page<NotificationHistoryRes> sendNotifyHistory(Pageable pageable) {
        String userId = TokenUtils.getUserId();

        log.info("notificationResPage userId: {}", userId);
        Page<NotificationRecipient> notificationRecipients = notificationRecipientRepository
                .findAllByRecipientId(userId, pageable);
        Page<NotificationHistoryRes> notificationRes = pageUtil.mapEntityPageIntoDtoPage(notificationRecipients,
                NotificationHistoryRes.class);
        List<Integer> notificationIds = notificationRecipients.get().map(NotificationRecipient::getNotificationId)
                .toList();
        List<Notification> notifications = notificationRepository.findNotificationsByIdIn(notificationIds);
        Map<Integer, Notification> notificationMap = notifications.stream()
                .filter(n -> n.getId() != null)
                .collect(Collectors.toMap(Notification::getId, Function.identity()));
        // Tạo map để lưu trạng thái đã đọc của từng thông báo
        // Sử dụng index để đảm bảo cùng thứ tự với kết quả trả về
        List<NotificationRecipient> recipientList = notificationRecipients.getContent();

        notificationRes.forEach(v -> {
            try {
                Notification notification = notificationMap.get(v.getNotificationId());
                if (notification != null) {
                    ExtraNotifyData extraNotifyData = JsonUtils.fromJson(v.getExtra(), ExtraNotifyData.class);
                    if (extraNotifyData != null) {
                        v.setTitle(notification.getTitle()
                                + (extraNotifyData.getTransId() == null ? "" : extraNotifyData.getTransId()));
                    } else {
                        v.setTitle(notification.getTitle());
                        v.setBody(notification.getBody());
                    }
                    v.setEvent(notification.getEvent().name());
                    // Thêm các trường mới cho deepLink
                    v.setButtonName(notification.getButtonName());
                    v.setButtonAction(notification.getButtonAction());

                    // Thêm trạng thái đã đọc
                    // Tìm NotificationRecipient tương ứng với thông báo này
                    for (NotificationRecipient recipient : recipientList) {
                        if (recipient.getNotificationId().equals(v.getNotificationId())) {
                            v.setIsRead(recipient.getIsRead() != null ? recipient.getIsRead() : false);
                            break;
                        }
                    }
                } else {
                    // trường hợp đơn ứng lương thành công ko có record ở bảng notification chỉ có
                    // bảng reception
                    ExtraNotifyData extraNotifyData = JsonUtils.fromJson(v.getExtra(), ExtraNotifyData.class);
                    if (extraNotifyData != null) {
                        v.setTitle("Đơn ứng "
                                + (extraNotifyData.getTransId() == null ? "" : extraNotifyData.getTransId()));
                        v.setBody("Đơn ứng của bạn đã giao dịch thành công.");
                        v.setEvent(NotificationEventType.SALARY_ADVANCE_SUCCESS.name());
                    }
                }
            } catch (JsonProcessingException e) {
                log.error(e.getMessage());
            }
        });
        return notificationRes;
    }

    @Override
    public Page<NotificationHistoryRes> getAllNotification(NotificationCriteriaDto criteria,
            Pageable pageable) {
        log.info("getAllNotification: getting all notifications directly from notification table with criteria");

        // Xây dựng Specification từ các tiêu chí tìm kiếm
        Specification<Notification> spec = notificationSpecification.getNotificationSpecification(criteria);

        // Lấy tất cả thông báo trực tiếp từ bảng Notification với các tiêu chí tìm kiếm
        Page<Notification> notifications = notificationRepository.findAll(spec, pageable);

        // Chuyển đổi từ Page<Notification> sang Page<NotificationHistoryRes> sử dụng
        // mapper
        Page<NotificationHistoryRes> notificationRes = notifications.map(modelMapper::toNotificationHistoryRes);

        return notificationRes;
    }

    @Override
    @Transactional
    public NotificationDetailRes copy(Integer id) {
        log.info("copy: copying notification with id {}", id);

        // Tìm thông báo gốc
        Notification originalNotification = notificationRepository.findNotificationById(id);
        if (originalNotification == null) {
            throw new ResourceNotFoundException("Không tìm thấy thông tin bản ghi id: " + id);
        }

        // Tạo một thông báo mới với thông tin từ thông báo gốc
        Notification newNotification = new Notification();
        newNotification.setEvent(originalNotification.getEvent());
        newNotification.setTitle(originalNotification.getTitle() + " (Bản sao)");
        newNotification.setBody(originalNotification.getBody());
        newNotification.setExtra(originalNotification.getExtra());
        newNotification.setPlatform(originalNotification.getPlatform());
        newNotification.setStatus(originalNotification.getStatus());
        newNotification.setButtonName(originalNotification.getButtonName());
        newNotification.setButtonAction(originalNotification.getButtonAction());
        newNotification.setStatus(NotificationStatus.ACTIVE);

        // Lưu thông báo mới vào cơ sở dữ liệu
        newNotification = notificationRepository.save(newNotification);

        log.info("copy: created new notification with id {}", newNotification.getId());

        // Chuyển đổi và trả về thông tin chi tiết của thông báo mới
        return modelMapper.toNotificationDetailRes(newNotification);
    }
}
