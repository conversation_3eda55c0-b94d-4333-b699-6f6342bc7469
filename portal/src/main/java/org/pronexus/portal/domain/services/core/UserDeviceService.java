package org.pronexus.portal.domain.services.core;

import org.pronexus.portal.app.dtos.CreateUserDeviceDto;
import org.pronexus.portal.app.dtos.UpdateUserDeviceDto;
import org.pronexus.portal.app.response.UserDeviceRes;

import java.util.List;

public interface UserDeviceService {
    Boolean createUserDevice(CreateUserDeviceDto dto);

    Boolean updateUserDevice(Integer id, UpdateUserDeviceDto dto);

    Boolean delUserDevice(Integer id);

    List<UserDeviceRes> getUserDevice(String userId);

    /**
     * <PERSON><PERSON><PERSON> danh sách token thiết bị của nhiều người dùng
     *
     * @param userIds Danh sách ID người dùng
     * @return Danh sách token thiết bị
     */
    List<String> getDeviceTokensByUserIds(List<String> userIds);

    Boolean deleteAllUserDevices(String userId);

    /**
     * X<PERSON>a liên kết user-partner theo userId
     *
     * @param userId ID của user
     * @return Boolean xác nhận xóa thành công
     */
    Boolean deleteUserPartner(String userId);

    /**
     * Xóa salary advance limit theo employeeId
     *
     * @param employeeId ID của employee
     * @return Boolean xác nhận xóa thành công
     */
    Boolean deleteSalaryAdvanceLimit(String employeeId);
}
