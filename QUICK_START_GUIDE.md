# Quick Start Guide - ZNS Token Refresh

## Bước 1: Database Setup

### Bảng tasks sẽ được tự động tạo:
- Sử dụng `spring.jpa.hibernate.ddl-auto=update`
- <PERSON><PERSON> thừa từ `AuditableEntity` (common-library)
- Tự động tracking audit fields: `createdAt`, `updatedAt`, `createdBy`, `updatedBy`
- Audit timestamps sử dụng Long (epoch milliseconds)
- Không cần migration scripts

### Nếu gặp lỗi "syntax error at group":
```bash
# Chạy script tạo bảng thủ công:
psql -d your_database -f job-schedule/manual_table_creation.sql
```

### Cấu hình đã thêm:
- `hibernate.default_schema=public`
- `hibernate.globally_quoted_identifiers=true`
- Field `"group"` được escape để tránh reserved keyword

## Bước 2: Thiết lập Configuration

### Thêm ZNS tokens vào database:
```sql
-- Thêm ZNS_FRESH_TOKEN (refresh token từ <PERSON>alo)
INSERT INTO portal.configurations (config_key, config_value, description, status) 
VALUES (
    'ZNS_FRESH_TOKEN', 
    '{"value": "vzW4EV3QAbtZrWaryD4cKU7M3pxrp5GkauvtIlN0NYIdl7O1_V850fN_JWVN_Na_..."}',
    'ZNS refresh token for OAuth',
    'ACTIVE'
) ON CONFLICT (config_key) DO UPDATE SET 
    config_value = EXCLUDED.config_value,
    status = 'ACTIVE';

-- Thêm ZNS_ACCESS_TOKEN (sẽ được cập nhật tự động)
INSERT INTO portal.configurations (config_key, config_value, description, status) 
VALUES (
    'ZNS_ACCESS_TOKEN', 
    '{"value": "initial_access_token_here"}',
    'ZNS access token for API calls',
    'ACTIVE'
) ON CONFLICT (config_key) DO UPDATE SET 
    config_value = EXCLUDED.config_value,
    status = 'ACTIVE';
```

## Bước 3: Lấy Access Token

### 3.1 Lấy token từ Keycloak:
```bash
curl -X POST https://identity.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials" \
  -d "client_id=pronexus_dev" \
  -d "client_secret=m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2"
```

### 3.2 Lưu access_token từ response để sử dụng

## Bước 4: Test API

### 4.1 Test refresh token thủ công:
```bash
curl -X POST https://api.pronexus.vn/user/api/v1/otp/refresh-token \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4.2 Tạo ZNS task:
```bash
curl -X POST https://api.pronexus.vn/job/tasks/zns-token-refresh \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4.3 Schedule task:
```bash
# Lấy task_id từ response ở bước 4.2, sau đó:
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/schedule \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4.4 Kiểm tra tasks:
```bash
curl -X GET https://api.pronexus.vn/job/tasks \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Bước 5: Monitoring

### Kiểm tra logs:
- User Service: Tìm "ZnsTokenServiceImpl" logs
- Job Schedule: Tìm "ZnsTokenRefreshJob" logs

### Kiểm tra database:
```sql
-- Xem tasks với audit info
SELECT id, name, job_type, is_active, cron_expression,
       created_at, updated_at, created_by, updated_by
FROM tasks;

-- Xem configurations
SELECT config_key, config_value->>'value' as value
FROM portal.configurations
WHERE config_key IN ('ZNS_ACCESS_TOKEN', 'ZNS_FRESH_TOKEN');
```

## Bước 6: Production Setup

### Thay đổi cron expression cho production:
```bash
# Tạo task với cron 30 phút
curl -X POST https://api.pronexus.vn/job/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "ZNS_TOKEN_REFRESH_PROD",
    "group": "ZNS_GROUP",
    "cronExpression": "0 */30 * * * ?",
    "jobType": "ZNS_TOKEN_REFRESH"
  }'

# Schedule task
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/schedule
```

## Troubleshooting

### Lỗi thường gặp:
1. **401 Unauthorized**: Lấy access token ở Bước 3
2. **Syntax error at "group"**: Chạy manual script ở Bước 1
3. **ZNS_FRESH_TOKEN not found**: Chạy lại Bước 2
4. **Feign error**: Kiểm tra network connectivity
5. **Job không chạy**: Kiểm tra Quartz logs
6. **Audit fields null**: Kiểm tra `@EnableJpaAuditing` configuration

### Commands hữu ích:
```bash
# Dừng job
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/unschedule

# Chạy lại job
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/schedule

# Xóa task
curl -X DELETE https://api.pronexus.vn/job/tasks/{task_id}
```
