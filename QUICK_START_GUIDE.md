# Quick Start Guide - ZNS Token Refresh

## Bước 1: <PERSON>hắ<PERSON> phục lỗi Database

### Nếu gặp lỗi "relation tasks does not exist":

#### Option A: Tự động (Flyway)
```bash
# Restart job-schedule service
# Flyway sẽ tự động tạo bảng tasks
```

#### Option B: Thủ công
```sql
-- Kết nối database và chạy:
CREATE TABLE IF NOT EXISTS tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    "group" VARCHAR(255) NOT NULL,
    cron_expression VARCHAR(255),
    job_type VARCHAR(100) NOT NULL DEFAULT 'TASK_JOB',
    is_active BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_tasks_job_type ON tasks(job_type);
CREATE INDEX IF NOT EXISTS idx_tasks_is_active ON tasks(is_active);
CREATE INDEX IF NOT EXISTS idx_tasks_group ON tasks("group");
```

## Bước 2: Thiết lập Configuration

### Thêm ZNS tokens vào database:
```sql
-- Thêm ZNS_FRESH_TOKEN (refresh token từ Zalo)
INSERT INTO portal.configurations (config_key, config_value, description, status) 
VALUES (
    'ZNS_FRESH_TOKEN', 
    '{"value": "vzW4EV3QAbtZrWaryD4cKU7M3pxrp5GkauvtIlN0NYIdl7O1_V850fN_JWVN_Na_..."}',
    'ZNS refresh token for OAuth',
    'ACTIVE'
) ON CONFLICT (config_key) DO UPDATE SET 
    config_value = EXCLUDED.config_value,
    status = 'ACTIVE';

-- Thêm ZNS_ACCESS_TOKEN (sẽ được cập nhật tự động)
INSERT INTO portal.configurations (config_key, config_value, description, status) 
VALUES (
    'ZNS_ACCESS_TOKEN', 
    '{"value": "initial_access_token_here"}',
    'ZNS access token for API calls',
    'ACTIVE'
) ON CONFLICT (config_key) DO UPDATE SET 
    config_value = EXCLUDED.config_value,
    status = 'ACTIVE';
```

## Bước 3: Test API

### 3.1 Test refresh token thủ công:
```bash
curl -X POST https://api.pronexus.vn/user/api/v1/otp/refresh-token
```

### 3.2 Tạo ZNS task:
```bash
curl -X POST https://api.pronexus.vn/job/tasks/zns-token-refresh
```

### 3.3 Schedule task:
```bash
# Lấy task_id từ response ở bước 3.2, sau đó:
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/schedule
```

### 3.4 Kiểm tra tasks:
```bash
curl -X GET https://api.pronexus.vn/job/tasks
```

## Bước 4: Monitoring

### Kiểm tra logs:
- User Service: Tìm "ZnsTokenServiceImpl" logs
- Job Schedule: Tìm "ZnsTokenRefreshJob" logs

### Kiểm tra database:
```sql
-- Xem tasks
SELECT id, name, job_type, is_active, cron_expression FROM tasks;

-- Xem configurations
SELECT config_key, config_value->>'value' as value 
FROM portal.configurations 
WHERE config_key IN ('ZNS_ACCESS_TOKEN', 'ZNS_FRESH_TOKEN');
```

## Bước 5: Production Setup

### Thay đổi cron expression cho production:
```bash
# Tạo task với cron 30 phút
curl -X POST https://api.pronexus.vn/job/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "ZNS_TOKEN_REFRESH_PROD",
    "group": "ZNS_GROUP",
    "cronExpression": "0 */30 * * * ?",
    "jobType": "ZNS_TOKEN_REFRESH"
  }'

# Schedule task
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/schedule
```

## Troubleshooting

### Lỗi thường gặp:
1. **Table not found**: Chạy lại Bước 1
2. **ZNS_FRESH_TOKEN not found**: Chạy lại Bước 2
3. **Feign error**: Kiểm tra network connectivity
4. **Job không chạy**: Kiểm tra Quartz logs

### Commands hữu ích:
```bash
# Dừng job
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/unschedule

# Chạy lại job
curl -X POST https://api.pronexus.vn/job/tasks/{task_id}/schedule

# Xóa task
curl -X DELETE https://api.pronexus.vn/job/tasks/{task_id}
```
