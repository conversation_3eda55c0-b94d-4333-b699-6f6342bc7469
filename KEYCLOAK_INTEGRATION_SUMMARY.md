# Keycloak Integration Summary - Job Schedule Service

## Đã thêm Keycloak Authentication

### 🔧 **Dependencies Added**
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-security</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
</dependency>
```

### 🛡️ **SecurityConfig.java**
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .csrf(AbstractHttpConfigurer::disable)
            .cors(Customizer.withDefaults())
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/actuator/**").permitAll()
                .requestMatchers("/health/**").permitAll()
                .anyRequest().authenticated())
            .oauth2ResourceServer(conf -> conf.jwt(Customizer.withDefaults()));
        
        return http.build();
    }

    @Bean
    public JwtDecoder jwtDecoder() {
        return NimbusJwtDecoder.withJwkSetUri(this.jwkSetUri).build();
    }
}
```

### ⚙️ **Configuration Properties**

#### **DEV/UAT Environment:**
```properties
# Security Configuration
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://identity.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/certs

# Keycloak Configuration
keycloak.realm=pronexus_dev
keycloak.auth-server-url=https://identity.pronexus.vn
keycloak.client-id=pronexus_dev
keycloak.client-secret=m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2
```

#### **LIVE Environment:**
```properties
# Security Configuration
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://auth.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/certs

# Keycloak Configuration
keycloak.auth-server-url=https://auth.pronexus.vn
```

### 📋 **Policy Enforcer**
```json
{
    "realm": "pronexus_dev",
    "auth-server-url": "https://identity.pronexus.vn",
    "resource": "pronexus_dev",
    "credentials": {
        "secret": "m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2"
    },
    "paths": [
        {
            "path": "/actuator/**",
            "enforcement-mode": "DISABLED"
        },
        {
            "path": "/health/**",
            "enforcement-mode": "DISABLED"
        }
    ]
}
```

## 🔑 **Authentication Flow**

### 1. Get Access Token
```bash
curl -X POST https://identity.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials" \
  -d "client_id=pronexus_dev" \
  -d "client_secret=m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2"
```

### 2. Use Token in API Calls
```bash
curl -X GET https://api.pronexus.vn/job/tasks \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 📱 **Postman Integration**

### Features Added:
- **Collection-level authentication**: Bearer token cho tất cả requests
- **Auto-token refresh**: Pre-request script tự động lấy token
- **Environment variables**: Keycloak configuration variables
- **Token management**: Tự động cache và refresh token

### Variables:
- `access_token`: Current access token
- `keycloak_url`: Keycloak server URL
- `realm`: Keycloak realm
- `client_id`: Client ID
- `client_secret`: Client secret

## 🎯 **Consistency với Other Services**

### Giống với:
- **Portal Service**: Cùng SecurityConfig pattern
- **Salary Advance Service**: Cùng JWT decoder configuration
- **File Service**: Cùng policy enforcer structure
- **User Service**: Cùng Keycloak properties

### Khác biệt:
- **Không có custom endpoints**: Tất cả endpoints đều require authentication
- **Minimal policy enforcer**: Chỉ disable cho health checks
- **No role-based access**: Chỉ cần valid token

## 🚀 **Benefits**

1. **Security**: Tất cả API endpoints được bảo vệ
2. **Consistency**: Nhất quán với architecture của project
3. **Centralized Auth**: Sử dụng chung Keycloak instance
4. **Token Management**: JWT-based stateless authentication
5. **Easy Testing**: Postman collection với auto-authentication

## 🔍 **Testing**

### Manual Testing:
```bash
# 1. Get token
TOKEN=$(curl -s -X POST https://identity.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials" \
  -d "client_id=pronexus_dev" \
  -d "client_secret=m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2" | jq -r '.access_token')

# 2. Test API
curl -X GET https://api.pronexus.vn/job/tasks \
  -H "Authorization: Bearer $TOKEN"
```

### Postman Testing:
1. Import collection: `user/ZNS_Token_Refresh.postman_collection.json`
2. Collection sẽ tự động handle authentication
3. Tất cả requests sẽ có Bearer token

## 🛠️ **Troubleshooting**

### Common Issues:
1. **401 Unauthorized**: Check token validity
2. **403 Forbidden**: Check token permissions
3. **Token expired**: Get new token
4. **Invalid client**: Check client credentials

### Debug Commands:
```bash
# Verify token
curl -X POST https://identity.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/token/introspect \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "token=YOUR_TOKEN" \
  -d "client_id=pronexus_dev" \
  -d "client_secret=m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2"
```

Job Schedule service giờ đã hoàn toàn nhất quán với security architecture của project! 🔐
