-- Manual database setup script for job-schedule service
-- Run this if Flyway migration doesn't work automatically

-- Create tasks table for job scheduling
CREATE TABLE IF NOT EXISTS tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    "group" VARCHAR(255) NOT NULL,
    cron_expression VARCHAR(255),
    job_type VARCHAR(100) NOT NULL DEFAULT 'TASK_JOB',
    is_active BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_tasks_job_type ON tasks(job_type);
CREATE INDEX IF NOT EXISTS idx_tasks_is_active ON tasks(is_active);
CREATE INDEX IF NOT EXISTS idx_tasks_group ON tasks("group");

-- Create trigger function to update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
<PERSON>ETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for tasks table
CREATE TRIGGER update_tasks_updated_at
    BEFORE UPDATE ON tasks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments
COMMENT ON TABLE tasks IS 'Table to store scheduled tasks information';
COMMENT ON COLUMN tasks.id IS 'Unique identifier for the task';
COMMENT ON COLUMN tasks.name IS 'Name of the task';
COMMENT ON COLUMN tasks."group" IS 'Group name for organizing tasks';
COMMENT ON COLUMN tasks.cron_expression IS 'Cron expression for scheduling';
COMMENT ON COLUMN tasks.job_type IS 'Type of job (TASK_JOB, ZNS_TOKEN_REFRESH, etc.)';
COMMENT ON COLUMN tasks.is_active IS 'Whether the task is currently scheduled/active';
COMMENT ON COLUMN tasks.created_at IS 'Timestamp when the task was created';
COMMENT ON COLUMN tasks.updated_at IS 'Timestamp when the task was last updated';

-- Insert sample ZNS token refresh task (optional)
INSERT INTO tasks (name, "group", cron_expression, job_type, is_active) 
VALUES ('ZNS_TOKEN_REFRESH', 'ZNS_GROUP', '*/10 * * * * ?', 'ZNS_TOKEN_REFRESH', false)
ON CONFLICT DO NOTHING;

-- Verify table creation
SELECT 'Tasks table created successfully' as status;
SELECT COUNT(*) as task_count FROM tasks;
