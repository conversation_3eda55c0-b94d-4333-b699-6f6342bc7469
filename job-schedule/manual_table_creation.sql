-- Manual table creation script for job-schedule service
-- Run this if Hibernate auto-creation fails

-- Set schema
SET search_path TO public;

-- Drop table if exists (for clean recreation)
DROP TABLE IF EXISTS public.tasks;

-- Create tasks table with proper escaping for reserved keywords
CREATE TABLE public.tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    "group" VARCHAR(255) NOT NULL,  -- Escaped reserved keyword
    cron_expression VARCHAR(255),
    job_type VARCHAR(255) NOT NULL DEFAULT 'TASK_JOB',
    is_active BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Audit fields from AuditableEntity
    created_at BIGINT,
    updated_at BIGINT,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    is_soft_deleted BOOLEAN DEFAULT FALSE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tasks_job_type ON public.tasks(job_type);
CREATE INDEX IF NOT EXISTS idx_tasks_is_active ON public.tasks(is_active);
CREATE INDEX IF NOT EXISTS idx_tasks_group ON public.tasks("group");
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON public.tasks(created_at);

-- Add comments
COMMENT ON TABLE public.tasks IS 'Table to store scheduled tasks information';
COMMENT ON COLUMN public.tasks.id IS 'Unique identifier for the task';
COMMENT ON COLUMN public.tasks.name IS 'Name of the task';
COMMENT ON COLUMN public.tasks."group" IS 'Group name for organizing tasks (escaped reserved keyword)';
COMMENT ON COLUMN public.tasks.cron_expression IS 'Cron expression for scheduling';
COMMENT ON COLUMN public.tasks.job_type IS 'Type of job (TASK_JOB, ZNS_TOKEN_REFRESH, etc.)';
COMMENT ON COLUMN public.tasks.is_active IS 'Whether the task is currently scheduled/active';
COMMENT ON COLUMN public.tasks.created_at IS 'Timestamp when the task was created (epoch milliseconds)';
COMMENT ON COLUMN public.tasks.updated_at IS 'Timestamp when the task was last updated (epoch milliseconds)';
COMMENT ON COLUMN public.tasks.created_by IS 'User who created the task';
COMMENT ON COLUMN public.tasks.updated_by IS 'User who last updated the task';
COMMENT ON COLUMN public.tasks.is_soft_deleted IS 'Soft delete flag from AuditableEntity';

-- Insert sample ZNS token refresh task (optional)
INSERT INTO public.tasks (name, "group", cron_expression, job_type, is_active, created_at, updated_at, created_by, updated_by) 
VALUES (
    'ZNS_TOKEN_REFRESH', 
    'ZNS_GROUP', 
    '*/10 * * * * ?', 
    'ZNS_TOKEN_REFRESH', 
    false,
    EXTRACT(EPOCH FROM NOW()) * 1000,
    EXTRACT(EPOCH FROM NOW()) * 1000,
    'system',
    'system'
) ON CONFLICT DO NOTHING;

-- Verify table creation
SELECT 'Tasks table created successfully in public schema' as status;
SELECT COUNT(*) as task_count FROM public.tasks;

-- Show table structure
\d public.tasks;
