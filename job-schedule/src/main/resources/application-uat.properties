# UAT Environment Configuration
server.port=8080

# Database Configuration - UAT
spring.datasource.url=******************************************************
spring.datasource.username=postgres
spring.datasource.password=Thanhnx@123$
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=200
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000

# JPA Configuration - UAT
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.default_schema=public
spring.jpa.properties.hibernate.globally_quoted_identifiers=true

# Flyway Configuration - UAT (disabled since using ddl-auto=update)
spring.flyway.enabled=false

# Quartz Configuration - UAT
spring.quartz.job-store-type=jdbc
spring.quartz.jdbc.initialize-schema=always
spring.quartz.properties.org.quartz.jobStore.class=org.springframework.scheduling.quartz.LocalDataSourceJobStore
spring.quartz.properties.org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
spring.quartz.properties.org.quartz.jobStore.isClustered=true
spring.quartz.properties.org.quartz.jobStore.useProperties=true
spring.quartz.properties.org.quartz.jobStore.tablePrefix=QRTZ_

# Feign Configuration - UAT
feign.user.url=https://api.pronexus.vn/user

# Logging Configuration - UAT
logging.level.org.springframework.web.client.RestTemplate=DEBUG
logging.level.org.springframework.cloud.openfeign.Feign=DEBUG