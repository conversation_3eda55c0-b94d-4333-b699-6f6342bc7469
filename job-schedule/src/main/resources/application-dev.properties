# Development Environment Configuration
server.port=8080

# Database Configuration - DEV
spring.datasource.url=******************************************************
spring.datasource.username=postgres
spring.datasource.password=Thanhnx@123$
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=200
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000

# JPA Configuration - DEV
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# Flyway Configuration - DEV (disabled since using ddl-auto=update)
spring.flyway.enabled=false

# Quartz Configuration - DEV
spring.quartz.job-store-type=jdbc
spring.quartz.jdbc.initialize-schema=always
spring.quartz.properties.org.quartz.jobStore.class=org.springframework.scheduling.quartz.LocalDataSourceJobStore
spring.quartz.properties.org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
spring.quartz.properties.org.quartz.jobStore.isClustered=true
spring.quartz.properties.org.quartz.jobStore.useProperties=true
spring.quartz.properties.org.quartz.jobStore.tablePrefix=QRTZ_

# Feign Configuration - DEV
feign.user.url=https://api.pronexus.vn/user
#feign.user.url=http://localhost:8002/user

# Logging Configuration - DEV
logging.level.org.springframework.web.client.RestTemplate=DEBUG
logging.level.org.springframework.cloud.openfeign.Feign=DEBUG
logging.level.org.quartz=DEBUG