package com.salaryadvance.jobschedule.controller;

import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.rest.Response;
import com.salaryadvance.jobschedule.dto.request.CreateTaskRequest;
import com.salaryadvance.jobschedule.dto.response.PagedTaskResponse;
import com.salaryadvance.jobschedule.dto.response.TaskResponse;
import com.salaryadvance.jobschedule.entity.Task;
import com.salaryadvance.jobschedule.enums.JobType;
import com.salaryadvance.jobschedule.service.TaskService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/api/v1/tasks")
@RequiredArgsConstructor
public class TaskController {
    private final TaskService taskService;

    /**
     * Tạo task (chỉ lưu DB, chưa schedule)
     */
    @PostMapping
    public ResponseEntity<Response<CommandResponse<TaskResponse>>> create(@RequestBody @Valid final CreateTaskRequest request) {
        Task task = taskService.create(request);
        TaskResponse taskResponse = TaskResponse.convert(task);
        CommandResponse<TaskResponse> commandResponse = CommandResponse.success(taskResponse, "Task created successfully");
        return Response.created(commandResponse);
    }

    /**
     * Tạo và schedule task ngay lập tức
     */
    @PostMapping("/create-and-schedule")
    public ResponseEntity<Response<CommandResponse<TaskResponse>>> createAndSchedule(@RequestBody @Valid final CreateTaskRequest request) {
        Task task = taskService.createAndSchedule(request);
        TaskResponse taskResponse = TaskResponse.convert(task);
        CommandResponse<TaskResponse> commandResponse = CommandResponse.success(taskResponse, "Task created and scheduled successfully");
        return Response.created(commandResponse);
    }

    /**
     * Schedule/Run một task đã có trong DB
     */
    @PostMapping("/{id}/schedule")
    public ResponseEntity<Response<CommandResponse<TaskResponse>>> scheduleTask(@PathVariable("id") final String id) {
        try {
            Task task = taskService.scheduleTask(UUID.fromString(id));
            TaskResponse taskResponse = TaskResponse.convert(task);
            CommandResponse<TaskResponse> commandResponse = CommandResponse.success(taskResponse, "Task scheduled successfully");
            return Response.success(commandResponse);
        } catch (Exception e) {
            log.error("Error scheduling task {}: {}", id, e.getMessage(), e);
            CommandResponse<TaskResponse> commandResponse = CommandResponse.failure(null, "Failed to schedule task: " + e.getMessage());
            return Response.failure(commandResponse);
        }
    }

    /**
     * Unschedule một task (hủy job nhưng vẫn giữ trong DB)
     */
    @PostMapping("/{id}/unschedule")
    public ResponseEntity<Response<CommandResponse<TaskResponse>>> unscheduleTask(@PathVariable("id") final String id) {
        Task task = taskService.unscheduleTask(UUID.fromString(id));
        TaskResponse taskResponse = TaskResponse.convert(task);
        CommandResponse<TaskResponse> commandResponse = CommandResponse.success(taskResponse, "Task unscheduled successfully");
        return Response.success(commandResponse);
    }

    /**
     * Lấy tất cả tasks với phân trang
     */
    @GetMapping
    public ResponseEntity<Response<PagedTaskResponse>> getAllTasks(
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "jobType", required = false) String jobType,
            @RequestParam(value = "isActive", required = false) Boolean isActive,
            @PageableDefault(page = 0, size = 10)
            @SortDefault.SortDefaults({
                    @SortDefault(sort = "createdAt", direction = Sort.Direction.DESC)
            })
            Pageable pageable) {
        Page<TaskResponse> taskPage = taskService.getAllTasks(name, jobType, isActive, pageable);
        PagedTaskResponse pagedResponse = PagedTaskResponse.from(taskPage);
        return Response.success(pagedResponse);
    }

    /**
     * Lấy tất cả tasks không phân trang (để backward compatibility)
     */
    @GetMapping("/all")
    public ResponseEntity<Response<List<TaskResponse>>> getAllTasksNoPaging() {
        List<Task> tasks = taskService.getAllTasksNoPaging();
        List<TaskResponse> responses = tasks.stream()
            .map(TaskResponse::convert)
            .toList();
        return Response.success(responses);
    }

    /**
     * Lấy task theo ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<Response<TaskResponse>> getTask(@PathVariable("id") final String id) {
        Task task = taskService.getTaskById(UUID.fromString(id));
        TaskResponse taskResponse = TaskResponse.convert(task);
        return Response.success(taskResponse);
    }

    /**
     * Xóa task
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Response<CommandResponse<Void>>> delete(@PathVariable("id") final String id) {
        taskService.delete(id);
        CommandResponse<Void> commandResponse = CommandResponse.success(null, "Task deleted successfully");
        return Response.success(commandResponse);
    }

    /**
     * Tạo ZNS token refresh task với cron expression mỗi 10 giây (chỉ lưu DB)
     */
    @PostMapping("/zns-token-refresh")
    public ResponseEntity<Response<CommandResponse<TaskResponse>>> createZnsTokenRefreshTask() {
        Task task = taskService.createZnsTokenRefreshTask("*/10 * * * * ?"); // Mỗi 10 giây
        TaskResponse taskResponse = TaskResponse.convert(task);
        CommandResponse<TaskResponse> commandResponse = CommandResponse.success(taskResponse, "ZNS token refresh task created successfully");
        return Response.created(commandResponse);
    }

    /**
     * Tạo và schedule ZNS token refresh task ngay lập tức
     */
    @PostMapping("/zns-token-refresh/schedule")
    public ResponseEntity<Response<CommandResponse<TaskResponse>>> createAndScheduleZnsTokenRefreshTask() {
        Task task = taskService.createAndScheduleZnsTokenRefreshTask("*/10 * * * * ?"); // Mỗi 10 giây
        TaskResponse taskResponse = TaskResponse.convert(task);
        CommandResponse<TaskResponse> commandResponse = CommandResponse.success(taskResponse, "ZNS token refresh task created and scheduled successfully");
        return Response.created(commandResponse);
    }
}
