package com.salaryadvance.jobschedule.controller;

import com.salaryadvance.jobschedule.dto.request.CreateTaskRequest;
import com.salaryadvance.jobschedule.dto.response.TaskResponse;
import com.salaryadvance.jobschedule.entity.Task;
import com.salaryadvance.jobschedule.enums.JobType;
import com.salaryadvance.jobschedule.service.TaskService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/tasks")
@RequiredArgsConstructor
public class TaskController {
    private final TaskService taskService;

    /**
     * Tạo task (chỉ lưu DB, chưa schedule)
     */
    @PostMapping
    public ResponseEntity<TaskResponse> create(@RequestBody @Valid final CreateTaskRequest request) {
        return new ResponseEntity<>(TaskResponse.convert(taskService.create(request)), HttpStatus.CREATED);
    }

    /**
     * Tạo và schedule task ngay lập tức
     */
    @PostMapping("/create-and-schedule")
    public ResponseEntity<TaskResponse> createAndSchedule(@RequestBody @Valid final CreateTaskRequest request) {
        return new ResponseEntity<>(TaskResponse.convert(taskService.createAndSchedule(request)), HttpStatus.CREATED);
    }

    /**
     * Schedule/Run một task đã có trong DB
     */
    @PostMapping("/{id}/schedule")
    public ResponseEntity<TaskResponse> scheduleTask(@PathVariable("id") final String id) {
        try {
            Task task = taskService.scheduleTask(UUID.fromString(id));
            return new ResponseEntity<>(TaskResponse.convert(task), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Unschedule một task (hủy job nhưng vẫn giữ trong DB)
     */
    @PostMapping("/{id}/unschedule")
    public ResponseEntity<TaskResponse> unscheduleTask(@PathVariable("id") final String id) {
        Task task = taskService.unscheduleTask(UUID.fromString(id));
        return new ResponseEntity<>(TaskResponse.convert(task), HttpStatus.OK);
    }

    /**
     * Lấy tất cả tasks
     */
    @GetMapping
    public ResponseEntity<List<TaskResponse>> getAllTasks() {
        List<Task> tasks = taskService.getAllTasks();
        List<TaskResponse> responses = tasks.stream()
            .map(TaskResponse::convert)
            .toList();
        return new ResponseEntity<>(responses, HttpStatus.OK);
    }

    /**
     * Lấy task theo ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<TaskResponse> getTask(@PathVariable("id") final String id) {
        Task task = taskService.getTaskById(UUID.fromString(id));
        return new ResponseEntity<>(TaskResponse.convert(task), HttpStatus.OK);
    }

    /**
     * Xóa task
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable("id") final String id) {
        taskService.delete(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    /**
     * Tạo ZNS token refresh task với cron expression mỗi 10 giây (chỉ lưu DB)
     */
    @PostMapping("/zns-token-refresh")
    public ResponseEntity<TaskResponse> createZnsTokenRefreshTask() {
        Task task = taskService.createZnsTokenRefreshTask("*/10 * * * * ?"); // Mỗi 10 giây
        return new ResponseEntity<>(TaskResponse.convert(task), HttpStatus.CREATED);
    }

    /**
     * Tạo và schedule ZNS token refresh task ngay lập tức
     */
    @PostMapping("/zns-token-refresh/schedule")
    public ResponseEntity<TaskResponse> createAndScheduleZnsTokenRefreshTask() {
        Task task = taskService.createAndScheduleZnsTokenRefreshTask("*/10 * * * * ?"); // Mỗi 10 giây
        return new ResponseEntity<>(TaskResponse.convert(task), HttpStatus.CREATED);
    }
}
