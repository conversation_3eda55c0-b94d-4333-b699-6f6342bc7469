package com.salaryadvance.jobschedule.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * Feign client để gọi User Service
 */
@FeignClient(name = "user-service", url = "${feign.user.url:http://localhost:8002/user}")
public interface UserServiceClient {

    /**
     * Gọi API refresh ZNS token
     * 
     * @return Response từ user service
     */
    @PostMapping("/api/v1/otp/refresh-token")
    ResponseEntity<Object> refreshZnsToken();
}
