package com.salaryadvance.jobschedule.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "tasks")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Task extends AbstractBaseEntity {
    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "group", nullable = false)
    private String group;

    @Column(name = "cron_expression")
    private String cronExpression;

    @Column(name = "job_type", nullable = false)
    private String jobType; // TASK_JOB, ZNS_TOKEN_REFRESH, etc.

    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = false; // false = chỉ lưu DB, true = đã đăng ký job
}
