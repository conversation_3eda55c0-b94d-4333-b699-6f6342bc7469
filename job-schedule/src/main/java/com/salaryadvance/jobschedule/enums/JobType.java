package com.salaryadvance.jobschedule.enums;

/**
 * Enum định nghĩa c<PERSON> lo<PERSON> job
 */
public enum JobType {
    TASK_JOB("TASK_JOB", "Default task job"),
    ZNS_TOKEN_REFRESH("ZNS_TOKEN_REFRESH", "ZNS token refresh job");

    private final String value;
    private final String description;

    JobType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
