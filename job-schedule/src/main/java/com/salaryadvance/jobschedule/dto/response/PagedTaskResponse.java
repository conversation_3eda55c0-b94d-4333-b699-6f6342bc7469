package com.salaryadvance.jobschedule.dto.response;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * Simplified paginated response for tasks
 */
@Getter
@Setter
@Builder
public class PagedTaskResponse {
    private List<TaskResponse> content;
    private int totalElements;
    private int totalPages;
    private int size;
    private int number;
    private boolean first;
    private boolean last;
    private boolean empty;

    /**
     * Convert Spring Page to simplified PagedTaskResponse
     *
     * @param page Spring Page object
     * @return PagedTaskResponse
     */
    public static PagedTaskResponse from(Page<TaskResponse> page) {
        return PagedTaskResponse.builder()
            .content(page.getContent())
            .totalElements((int) page.getTotalElements())
            .totalPages(page.getTotalPages())
            .size(page.getSize())
            .number(page.getNumber())
            .first(page.isFirst())
            .last(page.isLast())
            .empty(page.isEmpty())
            .build();
    }
}
