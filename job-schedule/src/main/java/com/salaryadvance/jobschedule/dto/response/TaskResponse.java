package com.salaryadvance.jobschedule.dto.response;

import com.salaryadvance.jobschedule.entity.Task;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
public class TaskResponse {
    private String id;

    private String name;

    private String group;

    private String cronExpression;

    private String jobType;

    private Boolean isActive;

    /**
     * Convert to TaskResponse
     *
     * @param task Task
     * @return TaskResponse
     */
    public static TaskResponse convert(Task task) {
        return TaskResponse.builder()
            .id(task.getId().toString())
            .name(task.getName())
            .group(task.getGroup())
            .cronExpression(task.getCronExpression())
            .jobType(task.getJobType())
            .isActive(task.getIsActive())
            .build();
    }
}
