package com.salaryadvance.commonlibrary.model;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.io.Serializable;
import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Auditable implements Serializable {
    @CreatedBy
    @Column(updatable = false)
    private String createdBy;

    @CreatedDate
    @Column(updatable = false)
    private Long createdAt = Instant.now().toEpochMilli();

    @LastModifiedBy
    private String updatedBy;

    @LastModifiedDate
    private Long updatedAt = Instant.now().toEpochMilli();
    private Boolean isSoftDeleted = false;
}
