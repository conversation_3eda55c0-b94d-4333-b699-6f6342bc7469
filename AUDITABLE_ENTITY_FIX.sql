-- Fix for AuditableEntity isSoftDeleted NULL values
-- Run this script to update existing records with NULL isSoftDeleted to false

-- Update portal schema tables
UPDATE portal.configurations SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;
UPDATE portal.partner SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;
UPDATE portal.administrative_units SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;
UPDATE portal.administrative_regions SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;
UPDATE portal.bank SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;
UPDATE portal.district SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;
UPDATE portal.ward SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;
UPDATE portal.department SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;
UPDATE portal.salary_advance_limit SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;
UPDATE portal.employee SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;

-- Update user schema tables
UPDATE user.otp SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;

-- Update salary_advance schema tables
UPDATE salary_advance.statistic SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;
UPDATE salary_advance.salary_repayment SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;

-- Update public schema tables
UPDATE public.tasks SET is_soft_deleted = false WHERE is_soft_deleted IS NULL;

-- Add NOT NULL constraint and default value for future records
-- Note: Only run these if you want to enforce NOT NULL constraint

-- For portal schema
-- ALTER TABLE portal.configurations ALTER COLUMN is_soft_deleted SET DEFAULT false;
-- ALTER TABLE portal.configurations ALTER COLUMN is_soft_deleted SET NOT NULL;

-- For user schema  
-- ALTER TABLE user.otp ALTER COLUMN is_soft_deleted SET DEFAULT false;
-- ALTER TABLE user.otp ALTER COLUMN is_soft_deleted SET NOT NULL;

-- For salary_advance schema
-- ALTER TABLE salary_advance.statistic ALTER COLUMN is_soft_deleted SET DEFAULT false;
-- ALTER TABLE salary_advance.statistic ALTER COLUMN is_soft_deleted SET NOT NULL;

-- For public schema
-- ALTER TABLE public.tasks ALTER COLUMN is_soft_deleted SET DEFAULT false;
-- ALTER TABLE public.tasks ALTER COLUMN is_soft_deleted SET NOT NULL;

-- Verify the updates
SELECT 'portal.configurations' as table_name, COUNT(*) as total_records, 
       COUNT(CASE WHEN is_soft_deleted IS NULL THEN 1 END) as null_count,
       COUNT(CASE WHEN is_soft_deleted = false THEN 1 END) as false_count,
       COUNT(CASE WHEN is_soft_deleted = true THEN 1 END) as true_count
FROM portal.configurations
UNION ALL
SELECT 'user.otp' as table_name, COUNT(*) as total_records,
       COUNT(CASE WHEN is_soft_deleted IS NULL THEN 1 END) as null_count,
       COUNT(CASE WHEN is_soft_deleted = false THEN 1 END) as false_count,
       COUNT(CASE WHEN is_soft_deleted = true THEN 1 END) as true_count
FROM user.otp
UNION ALL
SELECT 'public.tasks' as table_name, COUNT(*) as total_records,
       COUNT(CASE WHEN is_soft_deleted IS NULL THEN 1 END) as null_count,
       COUNT(CASE WHEN is_soft_deleted = false THEN 1 END) as false_count,
       COUNT(CASE WHEN is_soft_deleted = true THEN 1 END) as true_count
FROM public.tasks;
