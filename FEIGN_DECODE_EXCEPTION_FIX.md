# Feign Decode Exception Fix Summary

## 🐛 **Vấn đề gặp phải:**

### Error Message:
```
feign.codec.DecodeException: Could not extract response: no suitable HttpMessageConverter found for response type [class org.pronexus.user.app.dtos.zns.RefreshTokenResponseDto] and content type [text/json;charset=utf-8]
```

### Root Cause:
- Zalo OAuth API trả về content type `text/json;charset=utf-8`
- Spring Boot mặc định chỉ support `application/json`
- Feign không tìm thấy HttpMessageConverter phù hợp để decode response

## 🔧 **Giải pháp đã áp dụng:**

### 1. **Custom FeignConfig:**
```java
@Configuration
public class FeignConfig {

    @Bean
    public Decoder feignDecoder() {
        // Tạo Jackson converter với support cho text/json
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter();
        
        // Thêm text/json vào supported media types
        List<MediaType> supportedMediaTypes = new ArrayList<>(jacksonConverter.getSupportedMediaTypes());
        supportedMediaTypes.add(new MediaType("text", "json"));
        supportedMediaTypes.add(new MediaType("text", "json", StandardCharsets.UTF_8));
        jacksonConverter.setSupportedMediaTypes(supportedMediaTypes);
        
        ObjectFactory<HttpMessageConverters> messageConverters = () -> 
            new HttpMessageConverters(jacksonConverter);
        
        return new ResponseEntityDecoder(new SpringDecoder(messageConverters));
    }

    @Bean
    public Encoder feignEncoder() {
        // Support form data encoding
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter();
        ObjectFactory<HttpMessageConverters> messageConverters = () -> 
            new HttpMessageConverters(jacksonConverter);
        
        return new FormEncoder(new SpringEncoder(messageConverters));
    }
}
```

### 2. **Updated Feign Client:**
```java
@FeignClient(name = "zalo-oauth", url = "https://oauth.zaloapp.com", 
             configuration = org.pronexus.user.config.FeignConfig.class)
public interface ZaloOAuthFeignClient {

    // Primary method với auto-decode
    @PostMapping(value = "/v4/oa/access_token", 
                 consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE,
                 produces = MediaType.APPLICATION_JSON_VALUE)
    RefreshTokenResponseDto refreshAccessToken(
            @RequestHeader("secret_key") String secretKey,
            @RequestBody MultiValueMap<String, String> request
    );

    // Fallback method trả về String
    @PostMapping(value = "/v4/oa/access_token", 
                 consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String refreshAccessTokenAsString(
            @RequestHeader("secret_key") String secretKey,
            @RequestBody MultiValueMap<String, String> request
    );
}
```

### 3. **Fallback Mechanism trong Service:**
```java
@Service
public class ZnsTokenServiceImpl implements ZnsTokenService {
    
    private final ObjectMapper objectMapper;
    
    private RefreshTokenResponseDto callZaloRefreshTokenApi(String secretKey, MultiValueMap<String, String> requestBody) {
        try {
            // Thử auto-decode trước
            log.debug("Attempting to call Zalo API with auto-decode");
            return zaloOAuthFeignClient.refreshAccessToken(secretKey, requestBody);
            
        } catch (Exception e) {
            log.warn("Auto-decode failed, trying manual parsing: {}", e.getMessage());
            
            try {
                // Fallback: manual parsing
                String responseString = zaloOAuthFeignClient.refreshAccessTokenAsString(secretKey, requestBody);
                RefreshTokenResponseDto response = objectMapper.readValue(responseString, RefreshTokenResponseDto.class);
                log.info("Successfully parsed response with manual parsing");
                return response;
                
            } catch (Exception parseException) {
                log.error("Both auto-decode and manual parsing failed", parseException);
                throw new RuntimeException("Failed to parse Zalo API response", parseException);
            }
        }
    }
}
```

## 🎯 **Lợi ích của fix:**

### 1. **Robust Content Type Support:**
- Support cả `application/json` và `text/json`
- Handle charset encoding properly
- Compatible với Zalo API response format

### 2. **Fallback Mechanism:**
- Primary: Auto-decode với custom converter
- Fallback: Manual parsing nếu auto-decode fail
- Graceful error handling

### 3. **Form Data Support:**
- FormEncoder cho `application/x-www-form-urlencoded`
- Proper encoding cho Zalo OAuth API requirements

## 📊 **Test Cases:**

### 1. **Success với Auto-decode:**
```bash
curl -X POST https://api.pronexus.vn/user/api/v1/otp/refresh-token \
  -H "Authorization: Bearer TOKEN"

# Result: 200 OK với RefreshTokenResponseDto
# Log: "Attempting to call Zalo API with auto-decode"
```

### 2. **Success với Manual Parsing:**
```bash
# Nếu auto-decode fail
# Log: "Auto-decode failed, trying manual parsing"
# Log: "Successfully parsed response with manual parsing"
# Result: 200 OK với RefreshTokenResponseDto
```

### 3. **Complete Failure:**
```bash
# Nếu cả hai method đều fail
# Log: "Both auto-decode and manual parsing failed"
# Result: 500 Error với proper error message
```

## 🔍 **Monitoring và Debugging:**

### **Log Levels:**
```properties
# Enable debug logs để monitor
logging.level.org.pronexus.user.domain.service.impl.ZnsTokenServiceImpl=DEBUG
logging.level.org.pronexus.user.domain.feign.ZaloOAuthFeignClient=DEBUG
```

### **Success Indicators:**
- ✅ No DecodeException in logs
- ✅ Successful token refresh
- ✅ Updated ZNS_ACCESS_TOKEN in database

### **Monitoring Commands:**
```bash
# Check logs for decode issues
tail -f logs/application.log | grep -i "decode\|converter\|content-type"

# Test API directly
curl -X POST https://api.pronexus.vn/user/api/v1/otp/refresh-token \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🚀 **Alternative Solutions:**

### 1. **Global HttpMessageConverter:**
```java
@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setSupportedMediaTypes(Arrays.asList(
            MediaType.APPLICATION_JSON,
            new MediaType("text", "json")
        ));
        converters.add(converter);
    }
}
```

### 2. **Custom ResponseEntity:**
```java
// Nếu cần handle response headers
@PostMapping("/v4/oa/access_token")
ResponseEntity<RefreshTokenResponseDto> refreshAccessToken(...);
```

## ✅ **Verification:**

### **Before Fix:**
- ❌ DecodeException với text/json content type
- ❌ ZNS token refresh fail
- ❌ Job schedule không thể refresh token

### **After Fix:**
- ✅ Support text/json content type
- ✅ ZNS token refresh success
- ✅ Job schedule hoạt động bình thường
- ✅ Fallback mechanism cho reliability

## 📚 **Related Documentation:**
- [Spring HttpMessageConverter](https://docs.spring.io/spring-framework/docs/current/javadoc-api/org/springframework/http/converter/HttpMessageConverter.html)
- [Feign Configuration](https://docs.spring.io/spring-cloud-openfeign/docs/current/reference/html/)
- [Jackson MediaType Support](https://github.com/FasterXML/jackson-docs/wiki/JacksonHowToCustomSerializers)

Fix này đảm bảo ZNS token refresh hoạt động ổn định với Zalo API! 🎯
