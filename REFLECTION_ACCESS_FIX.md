# Reflection Access Fix Summary

## 🐛 **Vấn đề gặp phải:**

### Error Message:
```
java.lang.reflect.InaccessibleObjectException: Unable to make field transient java.util.HashMap$Node[] java.util.HashMap.table accessible: module java.base does not "opens java.util" to unnamed module
```

### Root Cause:
- Java 9+ module system (JPMS) không cho phép truy cập vào internal fields của `HashMap`
- Jackson/Feign cố gắng serialize `HashMap` bằng reflection
- X<PERSON>y ra khi gọi `zaloOAuthFeignClient.refreshAccessToken()` với `Map<String, String>` request body

## 🔧 **Giải pháp đã áp dụng:**

### 1. **Thay đổi Data Structure:**
```java
// Trước (có vấn đề reflection)
Map<String, String> requestBody = new HashMap<>();
requestBody.put("refresh_token", refreshToken);
requestBody.put("app_id", appId);
requestBody.put("grant_type", "refresh_token");

// Sau (tránh reflection issues)
MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
requestBody.add("refresh_token", refreshToken);
requestBody.add("app_id", appId);
requestBody.add("grant_type", "refresh_token");
```

### 2. **Cập nhật Feign Client:**
```java
// Trước
@PostMapping(value = "/v4/oa/access_token", 
             consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE,
             produces = MediaType.APPLICATION_JSON_VALUE)
RefreshTokenResponseDto refreshAccessToken(
        @RequestHeader("secret_key") String secretKey,
        @RequestBody Map<String, String> request
);

// Sau
@PostMapping(value = "/v4/oa/access_token", 
             consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE,
             produces = MediaType.APPLICATION_JSON_VALUE)
RefreshTokenResponseDto refreshAccessToken(
        @RequestHeader("secret_key") String secretKey,
        @RequestBody MultiValueMap<String, String> request
);
```

### 3. **Imports Updated:**
```java
// ZnsTokenServiceImpl.java
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

// ZaloOAuthFeignClient.java
import org.springframework.util.MultiValueMap;
```

## 🎯 **Lợi ích của fix:**

### 1. **Tránh Reflection Issues:**
- `MultiValueMap` được Spring hỗ trợ native cho form data
- Không cần reflection để access internal fields
- Compatible với Java module system

### 2. **Proper Form Encoding:**
- `MultiValueMap` được Feign encode đúng format `application/x-www-form-urlencoded`
- Phù hợp với API specification của Zalo OAuth

### 3. **Better Performance:**
- Không cần reflection overhead
- Native Spring support cho form data serialization

## 🔍 **Alternative Solutions (nếu vẫn có vấn đề):**

### 1. **JVM Arguments (Backup option):**
```bash
# Thêm vào JVM startup arguments
--add-opens java.base/java.util=ALL-UNNAMED
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.lang.reflect=ALL-UNNAMED
```

### 2. **Application Properties:**
```properties
# Disable reflection warnings (không khuyến khích)
spring.main.allow-bean-definition-overriding=true
```

### 3. **Jackson Configuration:**
```java
@Configuration
public class JacksonConfig {
    
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        return mapper;
    }
}
```

## 📊 **Test Cases:**

### 1. **Before Fix (Error):**
```bash
curl -X POST https://api.pronexus.vn/user/api/v1/otp/refresh-token \
  -H "Authorization: Bearer TOKEN"

# Result: InaccessibleObjectException
```

### 2. **After Fix (Success):**
```bash
curl -X POST https://api.pronexus.vn/user/api/v1/otp/refresh-token \
  -H "Authorization: Bearer TOKEN"

# Result: 200 OK with refreshed token
```

## 🚀 **Deployment Notes:**

### **Development:**
- Code changes đã fix vấn đề reflection
- Không cần thêm JVM arguments

### **Production:**
- Monitor logs để đảm bảo không có reflection warnings
- Backup plan: thêm JVM arguments nếu cần

### **Docker/Kubernetes:**
```yaml
# Nếu cần JVM arguments (backup)
env:
  - name: JAVA_OPTS
    value: "--add-opens java.base/java.util=ALL-UNNAMED"
```

## ✅ **Verification:**

### **Success Indicators:**
- ✅ No `InaccessibleObjectException` in logs
- ✅ ZNS token refresh API works correctly
- ✅ Feign client serializes form data properly
- ✅ Zalo OAuth API receives correct format

### **Test Commands:**
```bash
# Test refresh token endpoint
curl -X POST https://api.pronexus.vn/user/api/v1/otp/refresh-token \
  -H "Authorization: Bearer YOUR_TOKEN"

# Check logs for any reflection errors
tail -f logs/application.log | grep -i "reflection\|accessible"
```

## 📚 **Related Documentation:**
- [Java Module System (JPMS)](https://docs.oracle.com/javase/9/docs/api/java.base/module-summary.html)
- [Spring MultiValueMap](https://docs.spring.io/spring-framework/docs/current/javadoc-api/org/springframework/util/MultiValueMap.html)
- [Feign Form Encoding](https://github.com/OpenFeign/feign-form)

Fix này đảm bảo ZNS token refresh hoạt động ổn định với Java module system! 🎯
